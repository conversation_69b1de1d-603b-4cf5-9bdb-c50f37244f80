import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Check } from "lucide-react"

export default function ChallengePage() {
  const accountTypes = [
    {
      id: "step1",
      name: "Step 1",
      description: "The first phase of our two-step evaluation process",
      plans: [
        {
          capital: "$10,000",
          price: "$99",
          profitTarget: "10%",
          maxDrawdown: "8%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 8%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "Consistency Rule: 25%",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$249",
          profitTarget: "10%",
          maxDrawdown: "8%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 8%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "Consistency Rule: 25%",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$499",
          profitTarget: "10%",
          maxDrawdown: "8%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 8%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "Consistency Rule: 25%",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$999",
          profitTarget: "10%",
          maxDrawdown: "8%",
          profitSplit: "90%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 8%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "Consistency Rule: 25%",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
      ],
    },
    {
      id: "step2",
      name: "Step 2",
      description: "The second phase of our two-step evaluation process",
      plans: [
        {
          capital: "$10,000",
          price: "$79",
          profitTarget: "5%",
          maxDrawdown: "10%",
          profitSplit: "80%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 10%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "No Consistency Rule",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$199",
          profitTarget: "5%",
          maxDrawdown: "10%",
          profitSplit: "80%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 10%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "No Consistency Rule",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$399",
          profitTarget: "5%",
          maxDrawdown: "10%",
          profitSplit: "80%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 10%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "No Consistency Rule",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$1,249",
          profitTarget: "5%",
          maxDrawdown: "10%",
          profitSplit: "80%",
          features: [
            "1:100 Leverage",
            "Trade Forex, Indices, Commodities",
            "Max Daily Loss: 4%",
            "Max Total Loss: 10%",
            "Equity Based Drawdown",
            "News Trading: Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Minimum Trading Days: 5 Days",
            "No Consistency Rule",
            "Bi-Weekly Payouts",
            "24/7 Support",
          ],
          popular: false,
        },
      ],
    },
    {
      id: "instant",
      name: "Instant",
      description: "Skip the evaluation and get funded immediately",
      plans: [
        {
          capital: "$10,000",
          price: "$299",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Max Daily Loss: 2.5%",
            "Max Total Loss: 5%",
            "Equity Based Drawdown",
            "News Trading: Not Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Consistency Rule: 30%",
            "Bi-Weekly Payouts",
            "First Payout: 14 trading days + 30 trades",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$50,000",
          price: "$999",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Max Daily Loss: 2.5%",
            "Max Total Loss: 5%",
            "Equity Based Drawdown",
            "News Trading: Not Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Consistency Rule: 30%",
            "Bi-Weekly Payouts",
            "First Payout: 14 trading days + 30 trades",
            "24/7 Support",
          ],
          popular: true,
        },
        {
          capital: "$100,000",
          price: "$1,899",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Max Daily Loss: 2.5%",
            "Max Total Loss: 5%",
            "Equity Based Drawdown",
            "News Trading: Not Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Consistency Rule: 30%",
            "Bi-Weekly Payouts",
            "First Payout: 14 trading days + 30 trades",
            "24/7 Support",
          ],
          popular: false,
        },
        {
          capital: "$200,000",
          price: "$3,599",
          profitTarget: "None",
          maxDrawdown: "5%",
          profitSplit: "90%",
          features: [
            "1:50 Leverage",
            "Trade Forex, Indices, Commodities",
            "No evaluation phase",
            "Start trading immediately",
            "Max Daily Loss: 2.5%",
            "Max Total Loss: 5%",
            "Equity Based Drawdown",
            "News Trading: Not Allowed",
            "Weekend Holding: Not Allowed",
            "Overnight Holding: Allowed",
            "Consistency Rule: 30%",
            "Bi-Weekly Payouts",
            "First Payout: 14 trading days + 30 trades",
            "24/7 Support",
          ],
          popular: false,
        },
      ],
    },
  ]

  return (
    <main className="flex-1 py-20 bg-[#001a2c]">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold mb-4 text-white">Trading Challenges</h1>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Choose the perfect funding plan for your trading style and goals. All plans include our industry-leading
            features and support.
          </p>
        </div>

        <Tabs defaultValue="step1" className="w-full">
          <div className="flex justify-center mb-10 overflow-x-auto pb-2">
            <TabsList className="bg-[#002a3c]">
              {accountTypes.map((type) => (
                <TabsTrigger
                  key={type.id}
                  value={type.id}
                  className="data-[state=active]:bg-teal-500 data-[state=active]:text-white px-6"
                >
                  {type.name}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {accountTypes.map((accountType) => (
            <TabsContent key={accountType.id} value={accountType.id}>
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-white mb-2">{accountType.name}</h3>
                <p className="text-gray-400">{accountType.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {accountType.plans.map((plan, index) => (
                  <Card
                    key={index}
                    className={`bg-[#002a3c] border-[#003a4c] ${
                      plan.popular ? "border-teal-500 border-2" : "border-[#003a4c]"
                    } relative`}
                  >
                    {plan.popular && (
                      <div className="absolute top-0 right-0">
                        <div className="bg-teal-500 text-white text-xs font-bold px-3 py-1 transform translate-x-2 -translate-y-0">
                          POPULAR
                        </div>
                      </div>
                    )}
                    <CardHeader>
                      <CardTitle className="text-white">{accountType.name}</CardTitle>
                      <CardDescription className="text-gray-400">Trading Challenge</CardDescription>
                      <div className="mt-4">
                        <div className="text-3xl font-bold text-white">{plan.capital}</div>
                        <div className="text-teal-400 font-medium">{plan.price}</div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Profit Target</span>
                          <span className="font-medium text-white">{plan.profitTarget}</span>
                        </div>
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Max Drawdown</span>
                          <span className="font-medium text-white">{plan.maxDrawdown}</span>
                        </div>
                        <div className="flex justify-between items-center pb-2 border-b border-[#003a4c]">
                          <span className="text-gray-400">Profit Split</span>
                          <span className="font-medium text-white">{plan.profitSplit}</span>
                        </div>
                      </div>

                      <ul className="space-y-3 mt-6">
                        {plan.features.map((feature, idx) => (
                          <li key={idx} className="flex items-start">
                            <Check className="h-5 w-5 text-teal-400 mr-2 shrink-0" />
                            <span className="text-gray-300 text-sm">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                    <CardFooter>
                      <Button
                        className={`w-full ${
                          plan.popular
                            ? "bg-teal-500 hover:bg-teal-600 text-white"
                            : "bg-[#003a4c] text-white hover:bg-[#004a5c]"
                        }`}
                      >
                        Get Started
                      </Button>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </main>
  )
}
