"use client"

import { motion } from "framer-motion"
import { Facebook, Instagram } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

export default function Footer() {
  const socialLinks = [
    {
      name: "Discord",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-8 h-8"
        >
          <path d="M9 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M15 12a1 1 0 1 0 2 0a1 1 0 1 0 -2 0" />
          <path d="M8.5 17c0 1 -1.356 3 -1.832 3c-1.429 0 -2.698 -1.667 -3.333 -3c-.635 -1.667 -.476 -5.833 1.428 -11.5c1.388 -1.015 3.025 -1.34 4.5 -1.5l.238 2.5" />
          <path d="M14.5 17c0 1 1.5 3 2 3c1.5 0 2.764 -1.667 3.5 -3c.736 -1.333 .476 -5.833 -1.5 -11.5c-1.457 -1.015 -3.248 -1.34 -4.5 -1.5l-.25 2.5" />
          <path d="M7 16.5c3.5 1 6.5 1 10 0" />
        </svg>
      ),
      url: "https://discord.gg/fundedwhales",
    },
    {
      name: "Facebook",
      icon: <Facebook className="w-8 h-8" />,
      url: "https://facebook.com/fundedwhales",
    },
    {
      name: "Instagram",
      icon: <Instagram className="w-8 h-8" />,
      url: "https://instagram.com/fundedwhales",
    },
    {
      name: "Telegram",
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="w-8 h-8"
        >
          <path d="M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.96 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z" />
        </svg>
      ),
      url: "https://t.me/fundedwhales",
    },
  ]

  const footerLinks = [
    {
      title: "Company",
      links: [
        { name: "About Us", href: "/about" },
        { name: "Careers", href: "/careers" },
        { name: "Blog", href: "/blog" },
        { name: "Press", href: "/press" },
      ],
    },
    {
      title: "Resources",
      links: [
        { name: "Documentation", href: "/docs" },
        { name: "Help Center", href: "/help" },
        { name: "Community", href: "/community" },
        { name: "Webinars", href: "/webinars" },
      ],
    },
    {
      title: "Legal",
      links: [
        { name: "Terms of Service", href: "/terms" },
        { name: "Privacy Policy", href: "/privacy" },
        { name: "Risk Disclosure", href: "/risk" },
        { name: "Cookies", href: "/cookies" },
      ],
    },
  ]

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: { opacity: 1, y: 0 },
    hover: {
      scale: 1.1,
      color: "#38BDF8", // Changed from teal to sky
      transition: { duration: 0.3 },
    },
  }

  return (
    <footer className="bg-[#001525] border-t border-[#003a4c]">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Logo and description */}
          <div className="col-span-1">
            <Link href="/" className="flex items-center mb-4">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                alt="Funded Whales Logo"
                width={48}
                height={48}
                className="w-12 h-12 mr-3"
              />
              <span className="text-2xl font-bold text-white underwater-text">
                FUNDED<span className="text-sky-400">WHALES</span> {/* Changed from teal to sky */}
              </span>
            </Link>
            <p className="text-gray-400 mb-6">The leader in HFT friendly simulated funding challenges for traders.</p>

            {/* Social Media Section */}
            <motion.div
              variants={containerVariants}
              initial="hidden"
              whileInView="visible"
              viewport={{ once: true }}
              className="flex flex-wrap gap-4"
            >
              {socialLinks.map((social, index) => (
                <motion.a
                  key={index}
                  variants={itemVariants}
                  whileHover="hover"
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={`Follow us on ${social.name}`}
                  className="flex items-center justify-center w-14 h-14 rounded-full bg-[#002a3c] text-gray-300 hover:text-sky-400 hover:bg-[#003a4c] transition-all duration-300" // Changed from teal to sky
                >
                  {social.icon}
                </motion.a>
              ))}
            </motion.div>
          </div>

          {/* Footer links */}
          {footerLinks.map((section, i) => (
            <div key={i} className="col-span-1">
              <h3 className="text-white font-semibold mb-4">{section.title}</h3>
              <ul className="space-y-2">
                {section.links.map((link, j) => (
                  <li key={j}>
                    <Link href={link.href} className="text-gray-400 hover:text-sky-400 transition-colors">
                      {" "}
                      {/* Changed from teal to sky */}
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-12 pt-8 border-t border-[#003a4c] flex flex-col md:flex-row justify-between items-center">
          <p className="text-gray-500 text-sm mb-4 md:mb-0">
            © {new Date().getFullYear()} Funded Whales. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  )
}
