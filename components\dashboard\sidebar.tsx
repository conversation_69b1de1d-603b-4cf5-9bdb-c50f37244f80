"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  BarChart3,
  CreditCard,
  DollarSign,
  FileCheck,
  Gift,
  HelpCircle,
  Home,
  LogOut,
  Menu,
  MessageSquare,
  Settings,
  ShieldCheck,
  Users,
  X,
  ChevronLeft,
  ChevronRight,
  ShoppingCart,
} from "lucide-react"
import { useAuth } from "@/lib/auth-context"

interface SidebarProps {
  className?: string
  onToggle?: (collapsed: boolean) => void
}

export default function Sidebar({ className, onToggle }: SidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobileOpen, setIsMobileOpen] = useState(false)
  const { logout } = useAuth()

  const toggleSidebar = () => {
    const newCollapsed = !isCollapsed
    setIsCollapsed(newCollapsed)
    onToggle?.(newCollapsed)
  }

  const toggleMobileSidebar = () => {
    setIsMobileOpen(!isMobileOpen)
  }

  const routes = [
    {
      label: "Dashboard",
      icon: Home,
      href: "/dashboard",
      active: pathname === "/dashboard",
    },
    {
      label: "Buy Account",
      icon: ShoppingCart,
      href: "/dashboard/buy-account",
      active: pathname === "/dashboard/buy-account",
    },
    {
      label: "Trading Accounts",
      icon: BarChart3,
      href: "/dashboard/accounts",
      active: pathname === "/dashboard/accounts",
    },
    {
      label: "KYC Verification",
      icon: ShieldCheck,
      href: "/dashboard/kyc",
      active: pathname === "/dashboard/kyc",
    },
    {
      label: "Withdrawals",
      icon: DollarSign,
      href: "/dashboard/withdrawals",
      active: pathname === "/dashboard/withdrawals",
    },
    // Temporarily hidden pages - can be restored later
    // {
    //   label: "Referrals",
    //   icon: Users,
    //   href: "/dashboard/referrals",
    //   active: pathname === "/dashboard/referrals",
    // },
    // {
    //   label: "Billing",
    //   icon: CreditCard,
    //   href: "/dashboard/billing",
    //   active: pathname === "/dashboard/billing",
    // },
    // {
    //   label: "Certificates",
    //   icon: FileCheck,
    //   href: "/dashboard/certificates",
    //   active: pathname === "/dashboard/certificates",
    // },
    // {
    //   label: "Rewards",
    //   icon: Gift,
    //   href: "/dashboard/rewards",
    //   active: pathname === "/dashboard/rewards",
    // },
    // {
    //   label: "Support",
    //   icon: MessageSquare,
    //   href: "/dashboard/support",
    //   active: pathname === "/dashboard/support",
    // },
    // {
    //   label: "Help Center",
    //   icon: HelpCircle,
    //   href: "/dashboard/help",
    //   active: pathname === "/dashboard/help",
    // },
    // {
    //   label: "Settings",
    //   icon: Settings,
    //   href: "/dashboard/settings",
    //   active: pathname === "/dashboard/settings",
    // },
  ]

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && <div className="fixed inset-0 z-40 bg-black/80 lg:hidden" onClick={toggleMobileSidebar} />}

      {/* Mobile Toggle Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 right-4 z-50 lg:hidden text-white bg-white/10 backdrop-blur-sm hover:bg-white/20"
        onClick={toggleMobileSidebar}
      >
        {isMobileOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* Professional Right Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 right-0 z-40 flex flex-col transition-all duration-500 ease-in-out",
          "bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] backdrop-blur-2xl",
          "border-l border-sky-900/60 shadow-2xl",
          isCollapsed ? "w-[56px]" : "w-[220px]",
          isMobileOpen ? "translate-x-0" : "translate-x-full lg:translate-x-0",
          className,
        )}
      >
        {/* Professional Header */}
        <div className="relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-r from-sky-500/10 via-sky-600/10 to-sky-400/10" />
          <div className="relative flex items-center justify-between p-6 border-b border-sky-900/60">
            {/* Sidebar Toggle Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="text-gray-300 hover:text-white hover:bg-sky-500/20 rounded-xl transition-all duration-300 mr-2"
            >
              {isCollapsed ? <ChevronRight className="h-5 w-5" /> : <ChevronLeft className="h-5 w-5" />}
            </Button>
            {!isCollapsed && (
              <div className="flex flex-col">
                <Link href="/dashboard" className="flex items-center space-x-3 mb-2">
                  <Image
                    src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                    alt="Funded Whales Logo"
                    width={48}
                    height={48}
                    className="w-12 h-12"
                  />
                  <span className="text-2xl font-bold bg-gradient-to-r from-white via-sky-200 to-sky-400 bg-clip-text text-transparent">
                    FUNDED<span className="text-sky-400">WHALES</span>
                  </span>
                </Link>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse" />
                  <span className="text-sm text-gray-300">Professional Panel</span>
                </div>
              </div>
            )}
            {isCollapsed && (
              <div className="flex flex-col items-center w-full">
                <Image
                  src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                  alt="Funded Whales Logo"
                  width={48}
                  height={48}
                  className="w-12 h-12 mb-2"
                />
                <div className="w-2 h-2 bg-sky-400 rounded-full animate-pulse" />
              </div>
            )}
          </div>
        </div>

        {/* Professional Navigation */}
        <div className="flex-1 overflow-y-auto py-6 px-4">
          {!isCollapsed && (
            <div className="mb-6">
              <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3 px-3">
                Navigation
              </h3>
            </div>
          )}
          <nav className="space-y-2">
            {routes.map((route) => (
              <div key={route.href} className="relative group">
                <Link
                  href={route.href}
                  className={cn(
                    "flex items-center px-4 py-3 rounded-2xl text-sm font-medium transition-all duration-300 relative overflow-hidden",
                    route.active
                      ? "bg-gradient-to-r from-sky-500/20 via-sky-600/20 to-sky-400/20 text-white border border-sky-500/40 shadow-xl"
                      : "text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-sky-500/10 hover:to-sky-400/5 hover:border hover:border-sky-500/20",
                    isCollapsed ? "justify-center px-0 py-4" : ""
                  )}
                >
                  <route.icon className={cn(isCollapsed ? "h-7 w-7" : "h-5 w-5 mr-3")} />
                  {!isCollapsed && <span>{route.label}</span>}
                </Link>
              </div>
            ))}
          </nav>

          {/* Logout Section */}
          <div className={cn("pt-6 border-t border-sky-900/60", isCollapsed ? "flex flex-col items-center mt-8" : "mt-8")}>
            {!isCollapsed && (
              <div className="px-4">
                <h3 className="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                  Account
                </h3>
              </div>
            )}
            <div className={cn(isCollapsed ? "w-full flex justify-center" : "space-y-2")}>
              <Button
                variant="ghost"
                className={cn(
                  isCollapsed
                    ? "w-12 h-12 flex items-center justify-center text-gray-300 hover:text-white hover:bg-red-500/20 hover:border-red-500/40"
                    : "w-full justify-start text-gray-300 hover:text-white hover:bg-red-500/20 hover:border-red-500/40"
                )}
                onClick={logout}
              >
                <LogOut className={cn(isCollapsed ? "h-7 w-7" : "h-5 w-5 mr-3")} />
                {!isCollapsed && <span>Sign Out</span>}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
