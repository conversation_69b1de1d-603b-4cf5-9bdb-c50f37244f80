"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { getToken, removeToken } from './api'

interface AuthContextType {
  isAuthenticated: boolean
  token: string | null
  logout: () => void
  setToken: (token: string) => void
  isLoading: boolean
  checkTokenExpiration: () => boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [token, setTokenState] = useState<string | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Function to check if token is expired (without logout)
  const checkTokenExpiration = (): boolean => {
    if (!token) return false
    
    try {
      // Decode JWT token to check expiration
      const payload = JSON.parse(atob(token.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < currentTime) {
        // Token is expired
        return false
      }
      return true
    } catch (error) {
      // If token is malformed, consider it invalid
      return false
    }
  }

  // Function to validate token from localStorage
  const validateStoredToken = (storedToken: string): boolean => {
    try {
      // Decode JWT token to check expiration
      const payload = JSON.parse(atob(storedToken.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      
      if (payload.exp && payload.exp < currentTime) {
        // Token is expired
        return false
      }
      return true
    } catch (error) {
      // If token is malformed, consider it invalid
      return false
    }
  }

  // Function to handle logout
  const logout = () => {
    removeToken()
    setTokenState(null)
    setIsAuthenticated(false)
    setIsLoading(false)
    // Use window.location for more reliable navigation
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
  }

  // Function to handle token expiration
  const handleTokenExpiration = () => {
    removeToken()
    setTokenState(null)
    setIsAuthenticated(false)
    setIsLoading(false)
    // Use window.location for more reliable navigation
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login'
    }
  }

  useEffect(() => {
    // Check for existing token on mount
    const existingToken = getToken()
    if (existingToken) {
      // Validate token expiration before setting state
      if (validateStoredToken(existingToken)) {
        setTokenState(existingToken)
        setIsAuthenticated(true)
      } else {
        // Token is expired, handle expiration
        handleTokenExpiration()
        return // Don't set loading to false here
      }
    }
    setIsLoading(false)
  }, [])

  // Set up periodic token validation only after initial load
  useEffect(() => {
    if (token && !isLoading) {
      const interval = setInterval(() => {
        if (!checkTokenExpiration()) {
          // Token has expired, handle expiration
          handleTokenExpiration()
        }
      }, 60000) // Check every minute

      return () => clearInterval(interval)
    }
  }, [token, isLoading])

  const setToken = (newToken: string) => {
    setTokenState(newToken)
    setIsAuthenticated(true)
    setIsLoading(false)
  }

  // Add a global function for testing (only in development)
  if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
    (window as any).setTestToken = (testToken: string) => {
      localStorage.setItem('access_token', testToken)
      setToken(testToken)
      console.log('Test token set successfully!')
    }
  }

  return (
    <AuthContext.Provider value={{ 
      isAuthenticated, 
      token, 
      logout, 
      setToken, 
      isLoading,
      checkTokenExpiration 
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
} 