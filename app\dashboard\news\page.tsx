"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Newspaper, Globe, TrendingUp, Clock, ExternalLink, RefreshCw } from "lucide-react"

interface NewsItem {
  id: string
  title: string
  description: string
  time: string
  impact: 'high' | 'medium' | 'low'
  currency: string
  source: string
  url?: string
}

export default function NewsPage() {
  const [news, setNews] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState(true)
  const [mounted, setMounted] = useState(false)

  // Mock news data (in a real implementation, you'd fetch from ForexFactory API or RSS feed)
  const mockNews: NewsItem[] = [
    {
      id: '1',
      title: 'Federal Reserve Announces Interest Rate Decision',
      description: 'The Federal Reserve has announced its latest interest rate decision, maintaining rates at current levels while signaling potential future adjustments based on economic indicators.',
      time: '2 hours ago',
      impact: 'high',
      currency: 'USD',
      source: 'Federal Reserve',
      url: 'https://forexfactory.com'
    },
    {
      id: '2',
      title: 'European Central Bank Policy Meeting Results',
      description: 'ECB maintains accommodative monetary policy stance amid ongoing economic uncertainties in the eurozone.',
      time: '4 hours ago',
      impact: 'high',
      currency: 'EUR',
      source: 'European Central Bank',
      url: 'https://forexfactory.com'
    },
    {
      id: '3',
      title: 'UK Employment Data Shows Mixed Results',
      description: 'Latest employment figures from the UK show unemployment rate steady while wage growth continues to moderate.',
      time: '6 hours ago',
      impact: 'medium',
      currency: 'GBP',
      source: 'ONS',
      url: 'https://forexfactory.com'
    },
    {
      id: '4',
      title: 'Japanese Yen Strengthens on Safe Haven Demand',
      description: 'The Japanese Yen has gained strength against major currencies as investors seek safe haven assets amid market volatility.',
      time: '8 hours ago',
      impact: 'medium',
      currency: 'JPY',
      source: 'Bank of Japan',
      url: 'https://forexfactory.com'
    },
    {
      id: '5',
      title: 'Australian Dollar Reacts to RBA Minutes',
      description: 'AUD shows volatility following the release of Reserve Bank of Australia meeting minutes revealing dovish sentiment.',
      time: '10 hours ago',
      impact: 'medium',
      currency: 'AUD',
      source: 'Reserve Bank of Australia',
      url: 'https://forexfactory.com'
    },
    {
      id: '6',
      title: 'Oil Prices Impact Canadian Dollar Movement',
      description: 'CAD experiences fluctuations as crude oil prices show significant movement in global markets.',
      time: '12 hours ago',
      impact: 'low',
      currency: 'CAD',
      source: 'Bank of Canada',
      url: 'https://forexfactory.com'
    }
  ]

  // Handle client-side mounting to prevent hydration issues
  useEffect(() => {
    setMounted(true)
  }, [])

  useEffect(() => {
    if (!mounted) return

    // Load news data
    const loadNews = async () => {
      setLoading(true)
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      setNews(mockNews)
      setLoading(false)
    }

    loadNews()

    // Load TradingView Economic Calendar Widget
    const loadTradingViewCalendar = () => {
      const calendarContainer = document.getElementById('ff-calendar-widget')
      if (!calendarContainer) return

      calendarContainer.innerHTML = ''

      // Create the widget container
      const widgetContainer = document.createElement('div')
      widgetContainer.className = 'tradingview-widget-container'
      widgetContainer.style.height = '100%'
      widgetContainer.style.width = '100%'

      // Create the widget content div
      const widgetContent = document.createElement('div')
      widgetContent.className = 'tradingview-widget-container__widget'
      widgetContent.style.height = 'calc(100% - 32px)'
      widgetContent.style.width = '100%'

      // Create the script element
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-events.js'
      script.async = true
      script.innerHTML = JSON.stringify({
        "colorTheme": "dark",
        "isTransparent": false,
        "width": "100%",
        "height": "600",
        "locale": "en",
        "importanceFilter": "-1,0,1",
        "countryFilter": "us,gb,jp,au,ca,ch,cn,de,fr,it,nz,kr,in,br,mx,za",
        "currencyFilter": "USD,EUR,GBP,JPY,AUD,CAD,CHF,NZD,CNY"
      })

      script.onload = () => {
        console.log('✅ Economic Calendar widget loaded successfully')
      }

      script.onerror = () => {
        console.error('❌ Failed to load Economic Calendar widget')
        const errorDiv = document.createElement('div')
        errorDiv.className = 'flex items-center justify-center h-full text-gray-400 p-8'
        errorDiv.innerHTML = '<div class="text-center"><p class="mb-2">⚠️ Economic Calendar unavailable</p><p class="text-sm">Please check your internet connection and refresh the page</p></div>'
        calendarContainer.innerHTML = ''
        calendarContainer.appendChild(errorDiv)
      }

      widgetContainer.appendChild(widgetContent)
      widgetContainer.appendChild(script)
      calendarContainer.appendChild(widgetContainer)
    }

    // Load Market Overview Widget
    const loadMarketOverview = () => {
      const overviewContainer = document.getElementById('market-overview-widget')
      if (!overviewContainer) return

      overviewContainer.innerHTML = ''

      // Create the widget container
      const widgetContainer = document.createElement('div')
      widgetContainer.className = 'tradingview-widget-container'
      widgetContainer.style.height = '100%'
      widgetContainer.style.width = '100%'

      // Create the widget content div
      const widgetContent = document.createElement('div')
      widgetContent.className = 'tradingview-widget-container__widget'
      widgetContent.style.height = 'calc(100% - 32px)'
      widgetContent.style.width = '100%'

      // Create the script element
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js'
      script.async = true
      script.innerHTML = JSON.stringify({
        "colorTheme": "dark",
        "dateRange": "12M",
        "showChart": true,
        "locale": "en",
        "width": "100%",
        "height": "400",
        "largeChartUrl": "",
        "isTransparent": false,
        "showSymbolLogo": true,
        "showFloatingTooltip": false,
        "plotLineColorGrowing": "rgba(135, 206, 235, 1)",
        "plotLineColorFalling": "rgba(255, 82, 82, 1)",
        "gridLineColor": "rgba(135, 206, 235, 0.1)",
        "scaleFontColor": "rgba(135, 206, 235, 0.8)",
        "belowLineFillColorGrowing": "rgba(135, 206, 235, 0.12)",
        "belowLineFillColorFalling": "rgba(255, 82, 82, 0.12)",
        "belowLineFillColorGrowingBottom": "rgba(135, 206, 235, 0)",
        "belowLineFillColorFallingBottom": "rgba(255, 82, 82, 0)",
        "symbolActiveColor": "rgba(135, 206, 235, 0.12)",
        "tabs": [
          {
            "title": "Forex",
            "symbols": [
              {"s": "FX:EURUSD", "d": "EUR/USD"},
              {"s": "FX:GBPUSD", "d": "GBP/USD"},
              {"s": "FX:USDJPY", "d": "USD/JPY"},
              {"s": "FX:USDCHF", "d": "USD/CHF"},
              {"s": "FX:AUDUSD", "d": "AUD/USD"},
              {"s": "FX:USDCAD", "d": "USD/CAD"},
              {"s": "FX:NZDUSD", "d": "NZD/USD"},
              {"s": "FX:EURGBP", "d": "EUR/GBP"}
            ],
            "originalTitle": "Forex"
          }
        ]
      })

      script.onload = () => {
        console.log('✅ Market Overview widget loaded successfully')
      }

      script.onerror = () => {
        console.error('❌ Failed to load Market Overview widget')
        const errorDiv = document.createElement('div')
        errorDiv.className = 'flex items-center justify-center h-full text-gray-400 p-8'
        errorDiv.innerHTML = '<div class="text-center"><p class="mb-2">⚠️ Market Overview unavailable</p><p class="text-sm">Please check your internet connection and refresh the page</p></div>'
        overviewContainer.innerHTML = ''
        overviewContainer.appendChild(errorDiv)
      }

      widgetContainer.appendChild(widgetContent)
      widgetContainer.appendChild(script)
      overviewContainer.appendChild(widgetContainer)
    }

    // Load TradingView News Widget
    const loadNewsWidget = () => {
      const newsContainer = document.getElementById('tradingview-news-widget')
      if (!newsContainer) return

      newsContainer.innerHTML = ''

      // Create the widget container
      const widgetContainer = document.createElement('div')
      widgetContainer.className = 'tradingview-widget-container'
      widgetContainer.style.height = '100%'
      widgetContainer.style.width = '100%'

      // Create the widget content div
      const widgetContent = document.createElement('div')
      widgetContent.className = 'tradingview-widget-container__widget'
      widgetContent.style.height = 'calc(100% - 32px)'
      widgetContent.style.width = '100%'

      // Create the script element
      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-timeline.js'
      script.async = true
      script.innerHTML = JSON.stringify({
        "feedMode": "all_symbols",
        "colorTheme": "dark",
        "isTransparent": false,
        "displayMode": "regular",
        "width": "100%",
        "height": "600",
        "locale": "en"
      })

      script.onload = () => {
        console.log('✅ News Timeline widget loaded successfully')
      }

      script.onerror = () => {
        console.error('❌ Failed to load News Timeline widget')
        const errorDiv = document.createElement('div')
        errorDiv.className = 'flex items-center justify-center h-full text-gray-400 p-8'
        errorDiv.innerHTML = '<div class="text-center"><p class="mb-2">⚠️ News Timeline unavailable</p><p class="text-sm">Please check your internet connection and refresh the page</p></div>'
        newsContainer.innerHTML = ''
        newsContainer.appendChild(errorDiv)
      }

      widgetContainer.appendChild(widgetContent)
      widgetContainer.appendChild(script)
      newsContainer.appendChild(widgetContainer)
    }

    // Add loading indicators first
    const containers = ['ff-calendar-widget', 'market-overview-widget', 'tradingview-news-widget']
    containers.forEach(id => {
      const container = document.getElementById(id)
      if (container) {
        // Clear any existing content to prevent hydration conflicts
        container.innerHTML = ''
        // Add loading indicator
        const loadingDiv = document.createElement('div')
        loadingDiv.className = 'flex items-center justify-center h-full'
        loadingDiv.innerHTML = '<div class="flex items-center space-x-2 text-sky-400"><div class="animate-spin rounded-full h-6 w-6 border-b-2 border-sky-400"></div><span>Loading widget...</span></div>'
        container.appendChild(loadingDiv)
      }
    })

    // Load widgets with a delay to ensure DOM is ready
    setTimeout(() => {
      loadTradingViewCalendar()
      loadMarketOverview()
      loadNewsWidget()
    }, 500)

    return () => {
      const containers = ['ff-calendar-widget', 'market-overview-widget', 'tradingview-news-widget']
      containers.forEach(id => {
        const container = document.getElementById(id)
        if (container) container.innerHTML = ''
      })
    }
  }, [mounted])

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-500/20 text-red-400 border-red-500/30'
      case 'medium': return 'bg-yellow-500/20 text-yellow-400 border-yellow-500/30'
      case 'low': return 'bg-green-500/20 text-green-400 border-green-500/30'
      default: return 'bg-gray-500/20 text-gray-400 border-gray-500/30'
    }
  }

  const getCurrencyColor = (currency: string) => {
    const colors: { [key: string]: string } = {
      'USD': 'bg-blue-500/20 text-blue-400 border-blue-500/30',
      'EUR': 'bg-purple-500/20 text-purple-400 border-purple-500/30',
      'GBP': 'bg-pink-500/20 text-pink-400 border-pink-500/30',
      'JPY': 'bg-orange-500/20 text-orange-400 border-orange-500/30',
      'AUD': 'bg-teal-500/20 text-teal-400 border-teal-500/30',
      'CAD': 'bg-indigo-500/20 text-indigo-400 border-indigo-500/30'
    }
    return colors[currency] || 'bg-gray-500/20 text-gray-400 border-gray-500/30'
  }

  const refreshNews = () => {
    setLoading(true)
    setTimeout(() => {
      setNews([...mockNews].sort(() => Math.random() - 0.5))
      setLoading(false)
    }, 1000)
  }

  // Prevent hydration issues by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen p-6 space-y-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2 text-sky-400">
            <RefreshCw className="h-8 w-8 animate-spin" />
            <span className="text-lg">Loading Market News...</span>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-r from-sky-500/20 to-sky-400/20 rounded-lg border border-sky-500/30">
              <Newspaper className="h-6 w-6 text-sky-400" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-white">Market News</h1>
              <p className="text-gray-400">Latest forex and financial market news</p>
            </div>
          </div>
          <Button 
            onClick={refreshNews}
            variant="outline" 
            className="border-sky-500/30 text-sky-400 hover:bg-sky-500/10"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <Tabs defaultValue="live-news" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 bg-[#002a3c]/50 border border-sky-500/20">
            <TabsTrigger
              value="live-news"
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <Newspaper className="h-4 w-4 mr-2" />
              Live News
            </TabsTrigger>
            <TabsTrigger
              value="news"
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <Newspaper className="h-4 w-4 mr-2" />
              Market News
            </TabsTrigger>
            <TabsTrigger
              value="overview"
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Market Overview
            </TabsTrigger>
            <TabsTrigger
              value="calendar"
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <Globe className="h-4 w-4 mr-2" />
              Economic Calendar
            </TabsTrigger>
          </TabsList>

          <TabsContent value="live-news" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Newspaper className="h-5 w-5 mr-2 text-sky-400" />
                  Real-time Market News & Analysis
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Live financial news, market analysis, and trading insights powered by TradingView
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="tradingview-news-widget"
                  className="w-full min-h-[600px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="news" className="space-y-4">
            {loading ? (
              <div className="grid gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <Card key={i} className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
                    <CardContent className="p-6">
                      <div className="animate-pulse space-y-3">
                        <div className="h-4 bg-gray-700 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                        <div className="h-3 bg-gray-700 rounded w-full"></div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="grid gap-4">
                {news.map((item) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20 hover:border-sky-400/40 transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center space-x-2">
                            <Badge className={getImpactColor(item.impact)}>
                              {item.impact.toUpperCase()}
                            </Badge>
                            <Badge className={getCurrencyColor(item.currency)}>
                              {item.currency}
                            </Badge>
                          </div>
                          <div className="flex items-center text-gray-400 text-sm">
                            <Clock className="h-4 w-4 mr-1" />
                            {item.time}
                          </div>
                        </div>
                        <h3 className="text-white font-semibold text-lg mb-2">{item.title}</h3>
                        <p className="text-gray-400 mb-3">{item.description}</p>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-500">Source: {item.source}</span>
                          {item.url && (
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="text-sky-400 hover:text-sky-300 hover:bg-sky-500/10"
                              onClick={() => window.open(item.url, '_blank')}
                            >
                              <ExternalLink className="h-4 w-4 mr-1" />
                              Read More
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          <TabsContent value="overview" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-sky-400" />
                  Real-time Market Overview
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Live forex market data and price movements
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="market-overview-widget"
                  className="w-full min-h-[400px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Globe className="h-5 w-5 mr-2 text-sky-400" />
                  Real-time Economic Calendar
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Live economic events and market impact analysis powered by TradingView
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="ff-calendar-widget"
                  className="w-full min-h-[600px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10"
                />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
