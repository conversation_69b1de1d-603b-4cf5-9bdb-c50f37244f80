"use client";
import Image from "next/image";
import Link from "next/link";

export default function MetaTraderSection() {
  return (
    <section className="w-full py-16 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235]">
      <div className="max-w-5xl mx-auto px-4 flex flex-col md:flex-row items-center gap-10">
        {/* Left: Headline and Description */}
        <div className="flex-1 text-center md:text-left">
          <h2 className="text-4xl md:text-5xl font-extrabold text-sky-400 mb-4 drop-shadow-lg">
            Trade with the Industry Standard Platforms
          </h2>
          <p className="text-lg md:text-xl text-gray-300 mb-6 max-w-xl">
            Our platform supports both <span className="font-semibold text-white">MetaTrader 4</span> and <span className="font-semibold text-white">MetaTrader 5</span> — the world's most trusted trading terminals for Forex, stocks, and more. Download and start trading with professional tools, advanced charting, and robust security.
          </p>
        </div>
        {/* Right: Platform Cards */}
        <div className="flex-1 flex flex-col md:flex-row gap-8 justify-center items-center">
          {/* MT4 Card */}
          <div className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 rounded-2xl shadow-xl p-8 flex flex-col items-center w-72 border border-sky-500/20 hover:border-sky-400/40 hover:scale-105 transition-all duration-300">
            <Image
              src="https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/512x512bb-removebg-preview_qnfrll.png"
              alt="MetaTrader 4 Logo"
              width={64}
              height={64}
              className="mb-4"
              priority
            />
            <h3 className="text-2xl font-bold text-white mb-2">MetaTrader 4</h3>
            <p className="text-gray-300 text-center mb-4 text-base">
              The classic platform trusted by millions of traders worldwide. Fast execution, advanced charting, and automated trading.
            </p>
            <Link
              href="https://www.metatrader4.com/en/download"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-sky-500 hover:bg-sky-600 text-white font-semibold px-6 py-2 rounded-lg shadow-md transition-colors"
            >
              Download MT4
            </Link>
          </div>
          {/* MT5 Card */}
          <div className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 rounded-2xl shadow-xl p-8 flex flex-col items-center w-72 border border-sky-500/20 hover:border-sky-400/40 hover:scale-105 transition-all duration-300">
            <Image
              src="https://res.cloudinary.com/dufcjjaav/image/upload/v1752130069/MT5-removebg-preview_1_lqagz7.png"
              alt="MetaTrader 5 Logo"
              width={64}
              height={64}
              className="mb-4"
              priority
            />
            <h3 className="text-2xl font-bold text-white mb-2">MetaTrader 5</h3>
            <p className="text-gray-300 text-center mb-4 text-base">
              The next-generation platform for multi-asset trading. Enhanced tools, more markets, and superior performance.
            </p>
            <Link
              href="https://www.metatrader5.com/en/download"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-block bg-sky-500 hover:bg-sky-600 text-white font-semibold px-6 py-2 rounded-lg shadow-md transition-colors"
            >
              Download MT5
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
} 