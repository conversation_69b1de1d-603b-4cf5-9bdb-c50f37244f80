"use client"

import { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { TrendingUp, Calendar, BarChart3, Loader2 } from "lucide-react"

export default function SymbolsPage() {
  const [loading, setLoading] = useState(true)
  useEffect(() => {
    // Load TradingView widgets with error handling
    const loadWidget = (containerId: string, scriptSrc: string, config: any) => {
      const container = document.getElementById(containerId)
      if (!container) return

      const script = document.createElement('script')
      script.src = scriptSrc
      script.async = true
      script.innerHTML = JSON.stringify(config)

      script.onerror = () => {
        console.error(`Failed to load TradingView widget: ${scriptSrc}`)
        container.innerHTML = '<div class="flex items-center justify-center h-full text-gray-400"><p>Failed to load widget. Please refresh the page.</p></div>'
      }

      container.appendChild(script)

      // Set loading to false after a delay to simulate widget loading
      setTimeout(() => setLoading(false), 2000)
    }

    // Symbol Overview Widget
    loadWidget('tradingview-symbol-widget', 'https://s3.tradingview.com/external-embedding/embed-widget-symbol-overview.js', {
      "symbols": [
        ["EURUSD", "FX:EURUSD|1D"],
        ["GBPUSD", "FX:GBPUSD|1D"],
        ["USDJPY", "FX:USDJPY|1D"],
        ["USDCHF", "FX:USDCHF|1D"],
        ["AUDUSD", "FX:AUDUSD|1D"],
        ["USDCAD", "FX:USDCAD|1D"]
      ],
      "chartOnly": false,
      "width": "100%",
      "height": "500",
      "locale": "en",
      "colorTheme": "dark",
      "autosize": true,
      "showVolume": false,
      "showMA": false,
      "hideDateRanges": false,
      "hideMarketStatus": false,
      "hideSymbolLogo": false,
      "scalePosition": "right",
      "scaleMode": "Normal",
      "fontFamily": "-apple-system, BlinkMacSystemFont, Trebuchet MS, Roboto, Ubuntu, sans-serif",
      "fontSize": "10",
      "noTimeScale": false,
      "valuesTracking": "1",
      "changeMode": "price-and-percent",
      "chartType": "area",
      "maLineColor": "#2962FF",
      "maLineWidth": 1,
      "maLength": 9,
      "backgroundColor": "rgba(0, 42, 60, 0.9)",
      "lineWidth": 2,
      "lineType": 0,
      "dateRanges": [
        "1d|1",
        "1m|30",
        "3m|60",
        "12m|1D",
        "60m|1W",
        "all|1M"
      ]
    })

    // Economic Calendar Widget
    loadWidget('tradingview-calendar-widget', 'https://s3.tradingview.com/external-embedding/embed-widget-economic-calendar.js', {
      "colorTheme": "dark",
      "isTransparent": false,
      "width": "100%",
      "height": "600",
      "locale": "en",
      "importanceFilter": "-1,0,1",
      "countryFilter": "us,gb,jp,au,ca,ch,cn,de,fr,it,nz"
    })

    // Forex Cross Rates Widget
    loadWidget('tradingview-cross-rates-widget', 'https://s3.tradingview.com/external-embedding/embed-widget-forex-cross-rates.js', {
      "width": "100%",
      "height": "400",
      "currencies": [
        "EUR",
        "USD",
        "JPY",
        "GBP",
        "CHF",
        "AUD",
        "CAD",
        "NZD"
      ],
      "isTransparent": false,
      "colorTheme": "dark",
      "locale": "en"
    })

    return () => {
      // Cleanup widgets on unmount
      const containers = ['tradingview-symbol-widget', 'tradingview-calendar-widget', 'tradingview-cross-rates-widget']
      containers.forEach(id => {
        const container = document.getElementById(id)
        if (container) container.innerHTML = ''
      })
    }
  }, [])

  return (
    <div className="min-h-screen p-6 space-y-6">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center space-x-3 mb-6">
          <div className="p-2 bg-gradient-to-r from-sky-500/20 to-sky-400/20 rounded-lg border border-sky-500/30">
            <TrendingUp className="h-6 w-6 text-sky-400" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white">Market Analysis</h1>
            <p className="text-gray-400">Real-time symbols, charts, and economic calendar</p>
          </div>
        </div>

        <Tabs defaultValue="symbols" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 bg-[#002a3c]/50 border border-sky-500/20">
            <TabsTrigger 
              value="symbols" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Symbols & Charts
            </TabsTrigger>
            <TabsTrigger 
              value="calendar" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Economic Calendar
            </TabsTrigger>
            <TabsTrigger 
              value="cross-rates" 
              className="data-[state=active]:bg-sky-500/20 data-[state=active]:text-white text-gray-400"
            >
              <TrendingUp className="h-4 w-4 mr-2" />
              Cross Rates
            </TabsTrigger>
          </TabsList>

          <TabsContent value="symbols" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-sky-400" />
                  Major Currency Pairs
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Live charts and price data for major forex pairs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="tradingview-symbol-widget"
                  className="w-full min-h-[500px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading TradingView widgets...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="calendar" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-sky-400" />
                  Economic Calendar
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Important economic events and market-moving news
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="tradingview-calendar-widget"
                  className="w-full min-h-[600px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading economic calendar...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="cross-rates" className="space-y-6">
            <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border-sky-500/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-sky-400" />
                  Forex Cross Rates
                </CardTitle>
                <CardDescription className="text-gray-400">
                  Real-time cross rates for major currencies
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div
                  id="tradingview-cross-rates-widget"
                  className="w-full min-h-[400px] bg-[#002a3c]/50 rounded-lg border border-sky-500/10 relative"
                >
                  {loading && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="flex items-center space-x-2 text-sky-400">
                        <Loader2 className="h-6 w-6 animate-spin" />
                        <span>Loading cross rates...</span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  )
}
