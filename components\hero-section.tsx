"use client"

import { useEffect, useRef, useState } from "react"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"

export default function HeroSection() {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const sectionRef = useRef<HTMLElement>(null)
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 })
  const [isLoaded, setIsLoaded] = useState(false)
  const [sectionHeight, setSectionHeight] = useState(0)

  // Set section height to viewport height
  useEffect(() => {
    const updateHeight = () => {
      setSectionHeight(window.innerHeight)
    }

    updateHeight()
    window.addEventListener("resize", updateHeight)

    return () => {
      window.removeEventListener("resize", updateHeight)
    }
  }, [])

  // Handle mouse movement for interactive effects
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      setMousePosition({
        x: e.clientX,
        y: e.clientY,
      })
    }

    window.addEventListener("mousemove", handleMouseMove)
    return () => {
      window.removeEventListener("mousemove", handleMouseMove)
    }
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      const { innerWidth, innerHeight } = window
      const dpr = window.devicePixelRatio || 1

      canvas.width = innerWidth * dpr
      canvas.height = innerHeight * dpr

      canvas.style.width = `${innerWidth}px`
      canvas.style.height = `${innerHeight}px`

      ctx.scale(dpr, dpr)
    }

    setCanvasDimensions()
    window.addEventListener("resize", setCanvasDimensions)

    // Bubble class for animation
    class Bubble {
      x: number
      y: number
      radius: number
      speed: number
      opacity: number
      color: string

      constructor() {
        this.x = canvas ? (Math.random() * canvas.width) / window.devicePixelRatio : 0
        this.y = canvas ? canvas.height / window.devicePixelRatio + Math.random() * 100 : 0
        this.radius = Math.random() * 5 + 1
        this.speed = Math.random() * 1 + 0.5
        this.opacity = Math.random() * 0.5 + 0.1
        // Randomly choose between white and sky bubbles
        this.color = Math.random() > 0.7 ? "14, 165, 233" : "255, 255, 255" // Changed to sky-400
      }

      update() {
        if (!canvas) return
        this.y -= this.speed
        // Add slight horizontal movement
        this.x += Math.sin(this.y * 0.01) * 0.5

        // Reset bubble when it goes off screen
        if (this.y < -this.radius * 2) {
          this.x = (Math.random() * canvas.width) / window.devicePixelRatio
          this.y = canvas.height / window.devicePixelRatio + Math.random() * 20
          this.radius = Math.random() * 5 + 1
        }
      }

      draw() {
        if (!ctx) return
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(${this.color}, ${this.opacity})`
        ctx.fill()

        // Add subtle glow
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.radius * 1.5, 0, Math.PI * 2)
        ctx.fillStyle = `rgba(${this.color}, ${this.opacity * 0.3})`
        ctx.fill()
      }
    }

    // Particle class for floating data points
    class DataParticle {
      x: number
      y: number
      size: number
      speed: number
      opacity: number
      type: string

      constructor() {
        this.x = canvas ? (Math.random() * canvas.width) / window.devicePixelRatio : 0
        this.y = canvas ? (Math.random() * canvas.height) / window.devicePixelRatio : 0
        this.size = Math.random() * 3 + 1
        this.speed = Math.random() * 0.5 + 0.2
        this.opacity = Math.random() * 0.3 + 0.1
        // Different types of data particles
        const types = ["$", "€", "¥", "£", "₿", "↑", "↓", "%"]
        this.type = types[Math.floor(Math.random() * types.length)]
      }

      update() {
        if (!canvas) return
        this.y -= this.speed
        this.x += Math.sin(this.y * 0.02) * 0.3

        // Reset particle when it goes off screen
        if (this.y < -10) {
          this.x = (Math.random() * canvas.width) / window.devicePixelRatio
          this.y = canvas.height / window.devicePixelRatio + Math.random() * 10
        }
      }

      draw() {
        if (!ctx) return
        ctx.font = `${this.size * 5}px monospace`
        ctx.fillStyle = `rgba(14, 165, 233, ${this.opacity})` // Changed to sky-400
        ctx.fillText(this.type, this.x, this.y)
      }
    }

    // Create bubbles and data particles
    const bubbles: Bubble[] = []
    for (let i = 0; i < 40; i++) {
      bubbles.push(new Bubble())
    }

    const dataParticles: DataParticle[] = []
    for (let i = 0; i < 15; i++) {
      dataParticles.push(new DataParticle())
    }

    // Create underwater effect
    const drawBackground = () => {
      // Create gradient for underwater effect
      const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height / window.devicePixelRatio)
      gradient.addColorStop(0, "rgba(30, 58, 138, 0.92)") // Dark blue at top
      gradient.addColorStop(1, "rgba(15, 23, 42, 0.95)") // Deeper blue at bottom

      ctx.fillStyle = gradient
      ctx.fillRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)

      // Draw light rays that respond to mouse movement
      const rayCount = 8
      const centerX = mousePosition.x || canvas.width / (2 * window.devicePixelRatio)

      for (let i = 0; i < rayCount; i++) {
        const angle = (i / rayCount) * Math.PI * 2
        const rayX = centerX + Math.cos(angle) * 100
        const width = Math.random() * 100 + 50

        ctx.beginPath()
        ctx.moveTo(rayX, 0)
        ctx.lineTo(rayX + width / 2, canvas.height / window.devicePixelRatio)
        ctx.lineTo(rayX - width / 2, canvas.height / window.devicePixelRatio)
        ctx.closePath()

        const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height / window.devicePixelRatio)
        gradient.addColorStop(0, "rgba(14, 165, 233, 0.1)") // Changed to sky-400
        gradient.addColorStop(1, "rgba(30, 58, 138, 0)")
        ctx.fillStyle = gradient
        ctx.fill()
      }

      // Create water ripple effect on mouse move
      if (mousePosition.x && mousePosition.y) {
        const rippleRadius = 50
        const rippleWidth = 2

        for (let i = 0; i < 3; i++) {
          const radius = rippleRadius + i * 20
          const opacity = 0.3 - i * 0.1

          ctx.beginPath()
          ctx.arc(mousePosition.x, mousePosition.y, radius, 0, Math.PI * 2)
          ctx.strokeStyle = `rgba(14, 165, 233, ${opacity})` // Changed to sky-400
          ctx.lineWidth = rippleWidth
          ctx.stroke()
        }
      }

      // Update and draw bubbles
      bubbles.forEach((bubble) => {
        bubble.update()
        bubble.draw()
      })

      // Update and draw data particles
      dataParticles.forEach((particle) => {
        particle.update()
        particle.draw()
      })
    }

    // Animation loop
    let animationFrameId: number

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio)
      drawBackground()
      animationFrameId = requestAnimationFrame(animate)
    }

    animate()

    // Cleanup
    return () => {
      window.removeEventListener("resize", setCanvasDimensions)
      cancelAnimationFrame(animationFrameId)
    }
  }, [mousePosition])

  // Text animation variants
  const titleVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.8,
        ease: "easeOut",
        staggerChildren: 0.1,
      },
    },
  }

  const letterVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: "easeOut",
      },
    },
  }

  const subtitleVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 1.2,
        ease: "easeOut",
      },
    },
  }

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 1.5,
        ease: "easeOut",
      },
    },
    hover: {
      scale: 1.05,
      boxShadow: "0px 0px 20px rgba(14, 165, 233, 0.6)", // Changed to sky-400
      transition: { duration: 0.3 },
    },
  }

  // Split text for letter animation
  const titleText = "Fueling Traders. Funding Dreams"
  const titleWords = titleText.split(" ")

  return (
    <section
      ref={sectionRef}
      className="relative overflow-hidden bg-[#001a2c]"
      style={{ height: `${sectionHeight}px` }}
    >
      {/* Background canvas */}
      <canvas ref={canvasRef} className="absolute inset-0 w-full h-full z-0" />

      {/* Whale image as background - more clearly visible now */}
      <div className="absolute inset-0 z-0 opacity-45 mix-blend-soft-light">
        <Image
          src="/images/whale-hero.png"
          alt=""
          fill
          className="object-cover object-center scale-110 brightness-125 contrast-110"
          priority
          aria-hidden="true"
          onLoad={() => setIsLoaded(true)}
        />
      </div>

      {/* Subtle overlay gradient to improve text readability */}
      <div className="absolute inset-0 z-0 bg-gradient-radial from-transparent to-[#001a2c]/60"></div>

      <div className="container mx-auto px-4 relative z-10 h-full flex items-center">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center w-full">
          {/* Left Side - Text Content */}
          <AnimatePresence>
            {isLoaded && (
              <motion.div initial="hidden" animate="visible" className="text-left lg:text-left text-center lg:text-left">
                <motion.h1
                  variants={titleVariants}
                  className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-white drop-shadow-md underwater-text"
                >
                  {titleWords.map((word, i) => (
                    <span key={i} className="inline-block mr-4">
                      {word.split("").map((char, j) => (
                        <motion.span
                          key={`${i}-${j}`}
                          variants={letterVariants}
                          className={`inline-block ${
                            word === "Dreams"
                              ? "text-transparent bg-clip-text bg-gradient-to-r from-sky-400 to-blue-400 glow-text"
                              : ""
                          }`}
                        >
                          {char}
                        </motion.span>
                      ))}
                    </span>
                  ))}
                </motion.h1>

                <motion.p
                  variants={subtitleVariants}
                  className="text-lg md:text-xl text-gray-300 mb-8 max-w-lg drop-shadow"
                >
                  Join 72,000+ traders who trust our funded accounts to explore the markets with confidence
                </motion.p>

                <motion.div variants={buttonVariants} className="flex flex-col sm:flex-row gap-4 justify-start">
                  <motion.div whileHover="hover">
                    <Button
                      size="lg"
                      className="bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white px-8 py-6 text-lg shadow-lg transition-all duration-300 button-glow"
                    >
                      Dive In Now
                    </Button>
                  </motion.div>
                  <motion.div whileHover="hover">
                    <Button
                      size="lg"
                      variant="outline"
                      className="border-sky-500/30 text-white hover:bg-sky-500/10 px-8 py-6 text-lg transition-all duration-300"
                    >
                      Explore Accounts
                    </Button>
                  </motion.div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Right Side - Animated Candles */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 1, delay: 0.5 }}
            className="hidden lg:flex items-center justify-center"
          >
            <div className="relative">
              <Image
                src="https://fundednext.com/_next/image?url=https%3A%2F%2Fdirslur24ie1a.cloudfront.net%2Ffundednext%2Fnew-lander%2Fcandles.gif&w=640&q=75"
                alt="Animated Trading Candles"
                width={600}
                height={400}
                className="w-full max-w-lg h-auto rounded-lg"
                priority
              />
              {/* Removed subtle glow effect and background overlay */}
            </div>
          </motion.div>

          {/* Mobile Animated Candles */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.8 }}
            className="lg:hidden flex items-center justify-center mt-8"
          >
            <div className="relative">
              <Image
                src="https://fundednext.com/_next/image?url=https%3A%2F%2Fdirslur24ie1a.cloudfront.net%2Ffundednext%2Fnew-lander%2Fcandles.gif&w=640&q=75"
                alt="Animated Trading Candles"
                width={400}
                height={300}
                className="w-full max-w-sm h-auto rounded-lg"
                priority
              />
              {/* Removed subtle glow effect and background overlay */}
            </div>
          </motion.div>
        </div>
      </div>

      {/* Scroll indicator */}
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 2, duration: 0.8 }}
        className="absolute bottom-10 left-0 right-0 flex justify-center"
      >
        <div className="flex flex-col items-center">
          <span className="text-gray-400 text-sm mb-2">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center p-1">
            <motion.div
              animate={{ y: [0, 8, 0] }}
              transition={{ repeat: Number.POSITIVE_INFINITY, duration: 1.5 }}
              className="w-2 h-2 bg-sky-400 rounded-full"
            />
          </div>
        </div>
      </motion.div>

      {/* Subtle trading chart pattern overlay */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#001a2c] to-transparent z-0"></div>
      <div className="absolute bottom-0 left-0 right-0 h-20 bg-[url('/images/chart-pattern.png')] bg-repeat-x bg-bottom opacity-10 z-0 chart-flow"></div>
    </section>
  )
}
