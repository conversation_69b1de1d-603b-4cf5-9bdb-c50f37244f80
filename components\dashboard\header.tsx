"use client"

import { useState, useEffect } from "react"
import { usePathname, useRouter } from "next/navigation"
import Image from "next/image"
import { User, HelpCircle, Search, BarChart3, Eye, EyeOff, Co<PERSON> } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuth } from "@/lib/auth-context"
import { getOrders, getOrderDetails } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import Link from "next/link"

interface HeaderProps {
  sidebarWidth: number
}

export default function Header({ sidebarWidth }: HeaderProps) {
  const pathname = usePathname()
  const router = useRouter()
  const { logout } = useAuth()
  const { toast } = useToast()
  const [searchQuery, setSearchQuery] = useState("")
  const [orders, setOrders] = useState<any[]>([])
  const [selectedOrder, setSelectedOrder] = useState<any>(null)
  const [showOrdersDropdown, setShowOrdersDropdown] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  // Get page title from pathname
  const getPageTitle = () => {
    const path = pathname.split("/").pop()
    if (!path || path === "dashboard") return "Dashboard"
    return path.charAt(0).toUpperCase() + path.slice(1)
  }

  const handleLogout = () => {
    logout()
    router.push("/auth/login")
  }

  // Fetch orders on component mount
  useEffect(() => {
    const fetchOrders = async () => {
      try {
        setIsLoading(true)
        console.log('Starting to fetch orders...')
        
        const ordersData = await getOrders()
        console.log('Orders fetched successfully:', ordersData)
        setOrders(ordersData)
      } catch (error) {
        console.error('Failed to fetch orders:', error)
        setOrders([]) // Set empty array on error
        toast({
          title: "Error",
          description: error instanceof Error ? error.message : "Failed to fetch orders. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchOrders()
  }, [toast])

  const handleOrderSelect = async (orderId: string) => {
    try {
      console.log('Selecting order with ID:', orderId)
      const orderDetails = await getOrderDetails(orderId)
      console.log('Order details fetched successfully:', orderDetails)
      setSelectedOrder(orderDetails)
      setShowOrdersDropdown(false)
    } catch (error) {
      console.error('Failed to fetch order details:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to fetch order details. Please try again.",
        variant: "destructive",
      })
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard.",
      duration: 2000,
    })
  }

  return (
    <header
      className="sticky top-0 z-30 flex h-16 items-center bg-gradient-to-r from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 backdrop-blur-lg border-b border-sky-900/60 px-4"
      style={{ marginLeft: `${sidebarWidth}px` }}
    >
      <div className="flex flex-1 items-center justify-between">
        <div className="flex items-center space-x-3">
          <Image
            src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
            alt="Funded Whales Logo"
            width={48}
            height={48}
            className="w-12 h-12"
          />
          <div className="flex flex-col">
            <h1 className="text-xl font-semibold text-white">{getPageTitle()}</h1>
            <span className="text-sm font-medium text-sky-400">Funded Whales</span>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Search */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-64 pl-10 bg-[#001a2c]/50 border-sky-900/60 text-white focus:border-sky-400 placeholder:text-gray-400"
            />
          </div>

          {/* Orders Dropdown */}
          <DropdownMenu open={showOrdersDropdown} onOpenChange={setShowOrdersDropdown}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center gap-2 text-gray-300 hover:text-white hover:bg-sky-500/20">
                <BarChart3 className="h-5 w-5" />
                <span className="hidden md:block">Orders</span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80 bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] backdrop-blur-lg border-sky-900/60 text-white max-h-96 overflow-y-auto">
              <DropdownMenuLabel>My Orders</DropdownMenuLabel>
              <DropdownMenuSeparator className="bg-sky-900/60" />
              {isLoading ? (
                <DropdownMenuItem className="text-gray-400">
                  Loading orders...
                </DropdownMenuItem>
              ) : orders.length === 0 ? (
                <DropdownMenuItem className="text-gray-400">
                  No orders found
                </DropdownMenuItem>
              ) : (
                orders.map((order) => (
                  <DropdownMenuItem 
                    key={order.id} 
                    onClick={() => handleOrderSelect(order.id)}
                    className="text-gray-300 hover:text-white hover:bg-sky-500/20 cursor-pointer"
                  >
                    <div className="flex flex-col w-full">
                      <div className="flex justify-between items-center">
                        <span className="font-medium">{order.id}</span>
                        <Badge variant="outline" className="text-xs border-sky-500/60 text-sky-300">
                          {order.status || 'Active'}
                        </Badge>
                      </div>
                      <span className="text-sm text-gray-400">{order.challenge_type || 'Challenge'}</span>
                    </div>
                  </DropdownMenuItem>
                ))
              )}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Help */}
          <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white hover:bg-sky-500/20">
            <HelpCircle className="h-5 w-5" />
          </Button>

          {/* Profile Button */}
          <Link href="/dashboard/profile">
            <Button variant="outline" className="flex items-center gap-2 text-white border-sky-500/40 hover:bg-sky-500/10">
              <User className="h-5 w-5" />
              <span className="hidden md:inline">Profile</span>
            </Button>
          </Link>
        </div>
      </div>

      {/* Order Details Dialog */}
      <Dialog open={!!selectedOrder} onOpenChange={() => setSelectedOrder(null)}>
        <DialogContent className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] backdrop-blur-lg border-sky-900/60 text-white max-w-2xl">
          <DialogHeader>
            <DialogTitle className="text-white">Order Details</DialogTitle>
            <DialogDescription className="text-gray-300">
              Account credentials and trading information
            </DialogDescription>
          </DialogHeader>
          
          {selectedOrder && (
            <div className="space-y-6">
              {/* Order Info */}
              <Card className="bg-[#001a2c]/50 border-sky-900/60">
                <CardHeader>
                  <CardTitle className="text-white">Order Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Order ID</label>
                      <p className="text-white font-mono">{selectedOrder.id || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Challenge Type</label>
                      <p className="text-white">{selectedOrder.challenge_type || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Account Size</label>
                      <p className="text-white">${selectedOrder.account_size?.toLocaleString() || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Profit Target</label>
                      <p className="text-white">${selectedOrder.profit_target?.toLocaleString() || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Platform</label>
                      <p className="text-white">{selectedOrder.platform?.toUpperCase() || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Status</label>
                      <Badge 
                        variant="outline" 
                        className={`text-xs border-sky-500/60 ${
                          selectedOrder.status === 'Active' ? 'text-green-400' : 'text-yellow-400'
                        }`}
                      >
                        {selectedOrder.status || 'Unknown'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Account Credentials */}
              <Card className="bg-[#001a2c]/50 border-sky-900/60">
                <CardHeader>
                  <CardTitle className="text-white">Account Credentials</CardTitle>
                  <CardDescription className="text-gray-300">
                    Keep these credentials secure and confidential
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Server</label>
                      <div className="flex items-center gap-2">
                        <Input 
                          value={selectedOrder.server || ''} 
                          readOnly 
                          className="bg-[#001a2c]/30 border-sky-900/60 text-white font-mono"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedOrder.server || '')}
                          className="border-sky-500/60 text-sky-300 hover:bg-sky-500/20"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="text-sm font-medium text-gray-300">Username</label>
                      <div className="flex items-center gap-2">
                        <Input 
                          value={selectedOrder.username || ''} 
                          readOnly 
                          className="bg-[#001a2c]/30 border-sky-900/60 text-white font-mono"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedOrder.username || '')}
                          className="border-sky-500/60 text-sky-300 hover:bg-sky-500/20"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-300">Platform Login</label>
                      <div className="flex items-center gap-2">
                        <Input 
                          value={selectedOrder.platform_login || ''} 
                          readOnly 
                          className="bg-[#001a2c]/30 border-sky-900/60 text-white font-mono"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedOrder.platform_login || '')}
                          className="border-sky-500/60 text-sky-300 hover:bg-sky-500/20"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-300">Platform Password</label>
                      <div className="flex items-center gap-2">
                        <Input 
                          type={showPassword ? "text" : "password"}
                          value={selectedOrder.platform_password || ''} 
                          readOnly 
                          className="bg-[#001a2c]/30 border-sky-900/60 text-white font-mono"
                        />
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowPassword(!showPassword)}
                          className="border-sky-500/60 text-sky-300 hover:bg-sky-500/20"
                        >
                          {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(selectedOrder.platform_password || '')}
                          className="border-sky-500/60 text-sky-300 hover:bg-sky-500/20"
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Additional Info */}
              <Card className="bg-[#001a2c]/50 border-sky-900/60">
                <CardHeader>
                  <CardTitle className="text-white">Additional Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-300">Session ID</label>
                      <p className="text-white font-mono">{selectedOrder.session_id || 'N/A'}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-300">Terminal ID</label>
                      <p className="text-white font-mono">{selectedOrder.terminal_id || 'N/A'}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </header>
  )
}
