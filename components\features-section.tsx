"use client"

import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Zap, Clock, Shield, BarChart2, Award, Percent, Star, Crown, Diamond, Layers, DollarSign, Users } from "lucide-react"

export default function FeaturesSection() {
  const features = [
    {
      icon: <Zap className="h-12 w-12 text-sky-400" />,
      title: "HFT Friendly",
      description: "Designed specifically for high-frequency traders with ultra-low latency and lightning-fast execution.",
      premium: true,
    },
    {
      icon: <Clock className="h-12 w-12 text-sky-400" />,
      title: "Instant Verification",
      description: "Get verified and start trading within minutes, not days. Premium onboarding experience.",
      premium: false,
    },
    {
      icon: <Shield className="h-12 w-12 text-sky-400" />,
      title: "Secure Platform",
      description: "Bank-grade security with multi-layer encryption to protect your account and trading data.",
      premium: true,
    },
    {
      icon: <BarChart2 className="h-12 w-12 text-sky-400" />,
      title: "Advanced Analytics",
      description: "Professional-grade analytics suite with real-time performance insights and risk management tools.",
      premium: false,
    },
    {
      icon: <Award className="h-12 w-12 text-sky-400" />,
      title: "Elite Challenges",
      description: "Exclusive trading challenges designed for serious traders seeking substantial funding opportunities.",
      premium: true,
    },
    {
      icon: <Percent className="h-12 w-12 text-sky-400" />,
      title: "Premium Profit Split",
      description: "Industry-leading profit splits up to 90% with transparent fee structure and no hidden costs.",
      premium: false,
    },
  ]

  return (
    <section className="py-24 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-96 h-96 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-80 h-80 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -100],
              opacity: [0, 0.8, 0],
              scale: [0.5, 1.2, 0.5],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 6,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
          {/* Left column */}
          <div className="lg:col-span-1 flex flex-col justify-between h-full">
            <div>
              <span className="text-sky-400 font-medium tracking-wider uppercase text-sm block mb-4">Why Choose Us</span>
              <h2 className="text-4xl md:text-5xl font-bold mb-4 text-white leading-tight">
                Why Traders Choose <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">Funded Whales</span>
              </h2>
              <p className="text-lg text-gray-300 mb-8 max-w-xs leading-relaxed">
                Professional trading platform designed for serious traders seeking funding opportunities.
              </p>
            </div>
            <div className="mt-8">
              <span className="text-gray-400 text-xs uppercase tracking-wider mb-2 block">There is more -</span>
              <ul className="space-y-3 text-base">
                <li className="flex items-center gap-3 text-gray-200">
                  <DollarSign className="h-5 w-5 text-sky-400" /> Low commissions
                </li>
                <li className="flex items-center gap-3 text-gray-200">
                  <Percent className="h-5 w-5 text-sky-400" /> Raw spreads
                </li>
                <li className="flex items-center gap-3 text-gray-200">
                  <Layers className="h-5 w-5 text-sky-400" /> Best packages
                </li>
                <li className="flex items-center gap-3 text-gray-200">
                  <Users className="h-5 w-5 text-sky-400" /> Affordable
                </li>
              </ul>
            </div>
          </div>
          {/* Right: 2x3 grid of features */}
          <div className="lg:col-span-2 grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                whileHover={{ y: -8, scale: 1.02 }}
                className="group"
              >
                <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative shadow-xl rounded-2xl backdrop-blur-md">
                  {/* Premium badge */}
                  {feature.premium && (
                    <div className="absolute top-4 right-4">
                      <div className="flex items-center bg-sky-500/20 border border-sky-400/30 rounded-full px-2 py-1">
                        <Star className="h-3 w-3 text-sky-400 mr-1" />
                        <span className="text-xs text-sky-400 font-medium">PRO</span>
                      </div>
                    </div>
                  )}
                  <CardHeader className="pb-4">
                    <div className="mb-4">
                      <div className="w-14 h-14 bg-gradient-to-br from-sky-500/20 to-blue-500/20 rounded-xl flex items-center justify-center border border-sky-400/30 shadow-md">
                        {feature.icon}
                      </div>
                    </div>
                    <CardTitle className="text-lg font-semibold text-white">
                      {feature.title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-gray-300 leading-relaxed">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center bg-sky-500/10 border border-sky-400/20 rounded-full px-6 py-3">
            <span className="text-sky-400 font-medium">Join 10,000+ Professional Traders</span>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
