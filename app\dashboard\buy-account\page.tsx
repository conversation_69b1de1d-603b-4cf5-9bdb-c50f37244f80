"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Loader2,
  CreditCard,
  QrCode,
  Upload,
  Check,
  DollarSign,
  ChevronRight,
  Star,
  AlertCircle,
  Info,
  Shield,
  Clock,
  Users,
  TrendingUp
} from "lucide-react"
import { createOrder } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"

export default function BuyAccountPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    challengeType: "",
    platform: "",
    size: "",
    paymentMethod: "",
    txid: "",
    proofImage: null as File | null,
  })
  
  const [showPayment, setShowPayment] = useState(false)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [selectedPrice, setSelectedPrice] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  const challengeTypes = [
    {
      value: "instant",
      label: "Instant",
      description: "Instant Funding Challenge",
      price: 0,
      popular: true,
      features: ["No evaluation", "Immediate access", "Fastest payout"],
      color: "from-yellow-400 to-orange-500",
      icon: <TrendingUp className="h-5 w-5" />
    },
    { 
      value: "hft",
      label: "HFT",
      description: "High-Frequency Trading Challenge",
      price: 0,
      popular: false,
      features: ["Unlimited trading time", "Advanced algorithms", "API trading"],
      color: "from-purple-500 to-pink-500",
      icon: <Clock className="h-5 w-5" />
    },
    { 
      value: "one-step",
      label: "One-Step",
      description: "One-Step Evaluation Challenge",
      price: 0,
      popular: false,
      features: ["Simple rules", "Quick evaluation", "Low cost"],
      color: "from-blue-500 to-cyan-500",
      icon: <Check className="h-5 w-5" />
    },
    { 
      value: "two-step",
      label: "Two-Step",
      description: "Two-Step Evaluation Challenge",
      price: 0,
      popular: false,
      features: ["Standard evaluation", "Best for beginners"],
      color: "from-green-500 to-emerald-500",
      icon: <Users className="h-5 w-5" />
    },
  ]

  const platforms = [
    { value: "mt4", label: "MetaTrader 4" },
    { value: "mt5", label: "MetaTrader 5" },
  ]

  const sizes = [
    { value: "1000", label: "$1,000", price: 45 },
    { value: "3000", label: "$3,000", price: 105 },
    { value: "5000", label: "$5,000", price: 185 },
    { value: "10000", label: "$10,000", price: 335 },
    { value: "25000", label: "$25,000", price: 706 },
    { value: "50000", label: "$50,000", price: 1544 },
    { value: "100000", label: "$100,000", price: 2744 },
    { value: "200000", label: "$200,000", price: 4520 },
    { value: "500000", label: "$500,000", price: 8984 },
  ]

  const paymentMethods = [
    { 
      value: "matic", 
      label: "Matic (Polygon)", 
      network: "Polygon",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/4713/small/matic-token-icon.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "usdt-trc20", 
      label: "USDT (TRC20)", 
      network: "TRON",
      address: "TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex`
    },
    { 
      value: "usdt-bep20", 
      label: "USDT (BEP20)", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "bnb", 
      label: "BNB", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "sol", 
      label: "Solana", 
      network: "Solana",
      address: "5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd",
      logo: "https://assets.coingecko.com/coins/images/4128/small/solana.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd`
    },
    { 
      value: "eth", 
      label: "Ethereum", 
      network: "Ethereum",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "btc", 
      label: "Bitcoin", 
      network: "Bitcoin",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
  ]

  const validateForm = () => {
    const errors: {[key: string]: string} = {}
    
    if (!formData.email) {
      errors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address"
    }
    
    if (!formData.challengeType) {
      errors.challengeType = "Please select a challenge type"
    }
    
    if (!formData.platform) {
      errors.platform = "Please select a trading platform"
    }
    
    if (!formData.size) {
      errors.size = "Please select an account size"
    }
    
    if (!formData.paymentMethod) {
      errors.paymentMethod = "Please select a payment method"
    }
    
    if (!formData.txid) {
      errors.txid = "Transaction ID is required"
    } else if (formData.txid.length < 10) {
      errors.txid = "Please enter a valid transaction ID"
    }
    
    if (!formData.proofImage) {
      errors.proofImage = "Payment proof is required"
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: "" }))
    }
    
    if (field === "paymentMethod" && value) {
      setShowPayment(true)
    }
    if (field === "size") {
      const selectedSize = sizes.find(s => s.value === value)
      setSelectedPrice(selectedSize?.price || 0)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
      const maxSize = 5 * 1024 * 1024 // 5MB
      
      if (!allowedTypes.includes(file.type)) {
        setValidationErrors(prev => ({ ...prev, proofImage: "Please upload a valid image file (JPEG, PNG)" }))
        return
      }
      
      if (file.size > maxSize) {
        setValidationErrors(prev => ({ ...prev, proofImage: "File size must be less than 5MB" }))
        return
      }
      
      setFormData(prev => ({ ...prev, proofImage: file }))
      setValidationErrors(prev => ({ ...prev, proofImage: "" }))
    }
  }

  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      const orderData = {
        email: formData.email,
        challenge_type: formData.challengeType,
        account_size: formData.size,
        platform: formData.platform,
        payment_method: formData.paymentMethod,
        txid: formData.txid,
        image: formData.proofImage!,
      }

      await createOrder(orderData)
      
      toast({
        title: "Order Placed Successfully!",
        description: "Your order has been submitted and is being processed. You will receive an email confirmation shortly.",
        duration: 5000,
      })

      // Redirect to dashboard immediately after successful order
      router.push('/dashboard')
    } catch (error) {
      console.error('Failed to place order:', error)
      toast({
        title: "Order Failed",
        description: error instanceof Error ? error.message : "Failed to place order. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentMethod = paymentMethods.find(pm => pm.value === formData.paymentMethod)
  const selectedChallengeType = challengeTypes.find(ct => ct.value === formData.challengeType)
  const selectedSize = sizes.find(s => s.value === formData.size)

  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
          <CardContent className="text-center p-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">
              Order Placed Successfully!
            </h2>
            <p className="text-gray-300 mb-6">
              Your order has been submitted and is being processed. You will receive an email confirmation shortly.
            </p>
            <Button 
              onClick={() => {
                setOrderPlaced(false)
                setFormData({
                  email: "",
                  challengeType: "",
                  platform: "",
                  size: "",
                  paymentMethod: "",
                  txid: "",
                  proofImage: null,
                })
                setSelectedPrice(0)
                setShowPayment(false)
                setValidationErrors({})
              }}
              className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white"
            >
              Place Another Order
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] text-white">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Place Order
          </h1>
          <p className="text-gray-300 text-lg">
            Select your challenge type and configure your account
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Challenge Type Selection */}
            <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
              <CardHeader>
                <CardTitle className="text-xl text-white">Select Challenge Type</CardTitle>
              </CardHeader>
              <CardContent>
                <Select value={formData.challengeType} onValueChange={(value) => handleInputChange("challengeType", value)}>
                  <SelectTrigger className="bg-[#001a2c]/50 border-sky-900/60 text-white focus:border-sky-400 focus:ring-sky-400">
                    <SelectValue placeholder="Select challenge type" />
                  </SelectTrigger>
                  <SelectContent className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] border-sky-900/60">
                    {challengeTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value} className="text-white hover:bg-sky-500/20">
                        <div className="flex items-center justify-between w-full">
                          <div className="flex items-center gap-3">
                            <div className="flex items-center gap-2">
                              <span className="text-lg font-semibold">{type.label}</span>
                              {type.popular && (
                                <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white border-0 text-xs">
                                  <Star className="h-3 w-3 mr-1" />
                                  Popular
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-sm text-gray-400">{type.description}</div>
                            <div className="text-xs text-gray-500">
                              {type.features.slice(0, 2).join(", ")}
                            </div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {validationErrors.challengeType && (
                  <p className="text-red-400 text-sm mt-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {validationErrors.challengeType}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Account Configuration */}
            <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
              <CardHeader>
                <CardTitle className="text-xl text-white">Account Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium text-gray-300 mb-2 block">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={`bg-[#001a2c]/50 border-sky-900/60 text-white placeholder-gray-400 focus:border-sky-400 focus:ring-sky-400 ${
                        validationErrors.email ? 'border-red-500' : ''
                      }`}
                      placeholder="Enter your email"
                    />
                    {validationErrors.email && (
                      <p className="text-red-400 text-sm mt-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {validationErrors.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="platform" className="text-sm font-medium text-gray-300 mb-2 block">
                      Trading Platform
                    </Label>
                    <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                      <SelectTrigger className={`bg-[#001a2c]/50 border-sky-900/60 text-white focus:border-sky-400 focus:ring-sky-400 ${
                        validationErrors.platform ? 'border-red-500' : ''
                      }`}>
                        <SelectValue placeholder="Select platform" />
                      </SelectTrigger>
                      <SelectContent className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] border-sky-900/60">
                        {platforms.map((platform) => (
                          <SelectItem key={platform.value} value={platform.value} className="text-white hover:bg-sky-500/20">
                            {platform.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {validationErrors.platform && (
                      <p className="text-red-400 text-sm mt-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {validationErrors.platform}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-300 mb-4 block">
                    Account Size
                  </Label>
                  <div className="grid grid-cols-3 md:grid-cols-5 gap-3">
                    {sizes.map((size) => (
                      <div
                        key={size.value}
                        className={`border rounded-lg p-4 cursor-pointer text-center transition-all duration-300 ${
                          formData.size === size.value
                            ? "border-sky-500 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 shadow-lg"
                            : "border-sky-900/60 hover:border-sky-500/50 bg-gradient-to-br from-[#001a2c]/30 to-[#002a3c]/30"
                        }`}
                        onClick={() => handleInputChange("size", size.value)}
                      >
                        <div className="font-semibold text-white mb-1">{size.label}</div>
                        <div className="text-sm text-sky-400">${size.price}</div>
                      </div>
                    ))}
                  </div>
                  {validationErrors.size && (
                    <p className="text-red-400 text-sm mt-2 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {validationErrors.size}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-sky-400" />
                  Order Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedChallengeType && (
                  <div className="flex justify-between items-center p-4 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 rounded-lg border border-sky-900/60">
                    <div>
                      <div className="font-medium text-white">{selectedChallengeType.label}</div>
                      <div className="text-sm text-gray-400">{selectedChallengeType.description}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-xl font-bold text-white">${selectedPrice}</div>
                      <div className="text-xs text-gray-500">Setup Fee</div>
                    </div>
                  </div>
                )}
                
                <div className="border-t border-sky-900/60 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-white">Total</span>
                    <span className="text-2xl font-bold text-sky-400">
                      ${selectedPrice}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment Method */}
            <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-green-400" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.value}
                      className={`border rounded-lg p-4 cursor-pointer text-center transition-all duration-300 ${
                        formData.paymentMethod === method.value
                          ? "border-sky-500 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 shadow-lg"
                          : "border-sky-900/60 hover:border-sky-500/50 bg-gradient-to-br from-[#001a2c]/30 to-[#002a3c]/30"
                      }`}
                      onClick={() => handleInputChange("paymentMethod", method.value)}
                    >
                      <div className="flex flex-col items-center space-y-2">
                        <img 
                          src={method.logo} 
                          alt={method.label}
                          className="w-8 h-8 rounded-full"
                          style={{ minWidth: 32, minHeight: 32 }}
                        />
                        <div>
                          <div className="font-semibold text-white text-sm">{method.label}</div>
                          <Badge variant="secondary" className="mt-1 bg-sky-500/20 text-sky-300 border-sky-500/40 text-xs">
                            {method.network}
                          </Badge>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {validationErrors.paymentMethod && (
                  <p className="text-red-400 text-sm mt-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {validationErrors.paymentMethod}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Payment Details */}
            {showPayment && selectedPaymentMethod && (
              <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
                <CardHeader>
                  <CardTitle className="text-xl text-white flex items-center gap-2">
                    <QrCode className="h-5 w-5 text-orange-400" />
                    Payment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="bg-white p-4 rounded-lg border inline-block">
                      <img 
                        src={selectedPaymentMethod.qrCode}
                        alt="QR Code"
                        className="w-32 h-32"
                      />
                    </div>
                    <div className="mt-4 p-4 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 rounded-lg border border-sky-900/60">
                      <div className="text-2xl font-bold text-white">
                        ${selectedPrice}
                      </div>
                      <div className="text-sm text-gray-400">Amount to Pay</div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Wallet Address
                      </Label>
                      <div className="p-3 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 rounded-lg border border-sky-900/60">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300 font-mono break-all">
                            {selectedPaymentMethod.address}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(selectedPaymentMethod.address)
                              toast({
                                title: "Address Copied",
                                description: "Wallet address copied to clipboard",
                                duration: 2000,
                              })
                            }}
                            className="text-sky-400 hover:text-sky-300 ml-2"
                          >
                            Copy
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Transaction ID (TXID)
                      </Label>
                      <Input
                        value={formData.txid}
                        onChange={(e) => handleInputChange("txid", e.target.value)}
                        className={`bg-[#001a2c]/50 border-sky-900/60 text-white placeholder-gray-400 focus:border-sky-400 focus:ring-sky-400 ${
                          validationErrors.txid ? 'border-red-500' : ''
                        }`}
                        placeholder="Enter transaction ID"
                      />
                      {validationErrors.txid && (
                        <p className="text-red-400 text-sm mt-2 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          {validationErrors.txid}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Payment Proof
                      </Label>
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className={`bg-[#001a2c]/50 border-sky-900/60 text-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-sky-600 file:text-white hover:file:bg-sky-700 ${
                            validationErrors.proofImage ? 'border-red-500' : ''
                          }`}
                        />
                      </div>
                      {formData.proofImage && (
                        <p className="text-green-400 text-sm mt-2 flex items-center">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {formData.proofImage.name}
                        </p>
                      )}
                      {validationErrors.proofImage && (
                        <p className="text-red-400 text-sm mt-2 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          {validationErrors.proofImage}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <Button 
              onClick={handlePlaceOrder}
              disabled={isSubmitting}
              className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white text-lg font-semibold py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              size="lg"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Processing Order...
                </>
              ) : (
                <>
                  <Shield className="mr-2 h-5 w-5" />
                  Place Order - ${selectedPrice}
                </>
              )}
            </Button>

            {/* Security Notice */}
            <Card className="bg-gradient-to-br from-[#001a2c]/50 via-[#002a3c]/50 to-[#003a4c]/50 backdrop-blur-xl border border-sky-900/60">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-sky-400 mt-0.5" />
                  <div className="text-sm text-gray-300">
                    <p className="font-medium text-white mb-1">Secure Payment</p>
                    <p>Your payment information is encrypted and secure. We use industry-standard security protocols to protect your data.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
