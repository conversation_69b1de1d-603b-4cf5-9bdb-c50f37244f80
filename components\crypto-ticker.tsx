"use client"

import { motion } from "framer-motion"
import { TrendingUp, TrendingDown } from "lucide-react"

export default function CryptoTicker() {
  const cryptoData = [
    { symbol: "BTC", name: "Bitcoin", price: "$43,250.67", change: "+2.34%", isPositive: true },
    { symbol: "ETH", name: "Ethereum", price: "$2,680.45", change: "+1.87%", isPositive: true },
    { symbol: "BNB", name: "Binance Coin", price: "$312.89", change: "-0.92%", isPositive: false },
    { symbol: "SOL", name: "Sol<PERSON>", price: "$98.76", change: "+5.23%", isPositive: true },
    { symbol: "ADA", name: "Cardano", price: "$0.52", change: "+0.78%", isPositive: true },
    { symbol: "DOT", name: "Polkadot", price: "$7.34", change: "-1.45%", isPositive: false },
    { symbol: "MATIC", name: "Polygon", price: "$0.89", change: "+3.12%", isPositive: true },
    { symbol: "LINK", name: "Chainlink", price: "$15.67", change: "+2.89%", isPositive: true },
    { symbol: "UNI", name: "Uniswap", price: "$6.78", change: "-0.34%", isPositive: false },
    { symbol: "AVAX", name: "Avalanche", price: "$23.45", change: "+4.67%", isPositive: true },
    { symbol: "ATOM", name: "Cosmos", price: "$9.12", change: "+1.23%", isPositive: true },
    { symbol: "LTC", name: "Litecoin", price: "$68.90", change: "-0.78%", isPositive: false },
  ]

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-[#001a2c]/95 backdrop-blur-md border-b border-sky-500/20 shadow-lg">
      <div className="relative overflow-hidden h-12">
        <motion.div
          className="flex items-center h-full"
          animate={{
            x: [0, -100 * cryptoData.length],
          }}
          transition={{
            duration: 60,
            repeat: Infinity,
            ease: "linear",
          }}
        >
          {[...cryptoData, ...cryptoData].map((crypto, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 px-6 py-2 border-r border-sky-500/10 min-w-max"
            >
              <div className="flex items-center space-x-2">
                <span className="text-white font-bold text-sm">{crypto.symbol}</span>
                <span className="text-gray-400 text-xs">{crypto.name}</span>
              </div>
              <span className="text-white font-semibold text-sm">{crypto.price}</span>
              <div className={`flex items-center space-x-1 ${crypto.isPositive ? 'text-green-400' : 'text-red-400'}`}>
                {crypto.isPositive ? (
                  <TrendingUp className="w-3 h-3" />
                ) : (
                  <TrendingDown className="w-3 h-3" />
                )}
                <span className="text-xs font-medium">{crypto.change}</span>
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  )
} 