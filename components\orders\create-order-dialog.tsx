"use client"

import { useState } from "react"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Save, DollarSign } from "lucide-react"

interface CreateOrderDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSubmit: (formData: any) => Promise<void>
}

export default function CreateOrderDialog({ open, onOpenChange, onSubmit }: CreateOrderDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    type: "",
    amount: "",
    userId: "",
    tradingRules: {
      maxDailyLoss: "",
      maxTotalLoss: "",
      profitTarget: "",
      minTradingDays: "5",
      maxTradingDays: "30",
    },
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      await onSubmit(formData)
      onOpenChange(false)
    } catch (error) {
      console.error("Failed to create order:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl bg-[#002a3c] border-[#003a4c] text-white">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold">Create New Order</h2>
            <p className="text-gray-400">Create a new trading challenge or live account</p>
          </div>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card className="bg-[#001a2c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Basic Information</CardTitle>
                <CardDescription className="text-gray-400">Enter the main order details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-gray-400">Challenge Type</Label>
                  <Select
                    value={formData.type}
                    onValueChange={(value) => setFormData({ ...formData, type: value })}
                    required
                  >
                    <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white">
                      <SelectValue placeholder="Select challenge type" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                      <SelectItem value="Standard Challenge">Standard Challenge</SelectItem>
                      <SelectItem value="Express Challenge">Express Challenge</SelectItem>
                      <SelectItem value="HFT Challenge">HFT Challenge</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-400">Amount</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      type="number"
                      placeholder="Enter amount"
                      value={formData.amount}
                      onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                      className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-400">User ID</Label>
                  <Input
                    placeholder="Enter user ID"
                    value={formData.userId}
                    onChange={(e) => setFormData({ ...formData, userId: e.target.value })}
                    className="bg-[#001a2c] border-[#003a4c] text-white"
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Trading Rules */}
            <Card className="bg-[#001a2c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Trading Rules</CardTitle>
                <CardDescription className="text-gray-400">Set the trading parameters and limits</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label className="text-gray-400">Maximum Daily Loss</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      type="number"
                      placeholder="Enter max daily loss"
                      value={formData.tradingRules.maxDailyLoss}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tradingRules: { ...formData.tradingRules, maxDailyLoss: e.target.value },
                        })
                      }
                      className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-400">Maximum Total Loss</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      type="number"
                      placeholder="Enter max total loss"
                      value={formData.tradingRules.maxTotalLoss}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tradingRules: { ...formData.tradingRules, maxTotalLoss: e.target.value },
                        })
                      }
                      className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-400">Profit Target</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                    <Input
                      type="number"
                      placeholder="Enter profit target"
                      value={formData.tradingRules.profitTarget}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tradingRules: { ...formData.tradingRules, profitTarget: e.target.value },
                        })
                      }
                      className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-gray-400">Min Trading Days</Label>
                    <Input
                      type="number"
                      value={formData.tradingRules.minTradingDays}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tradingRules: { ...formData.tradingRules, minTradingDays: e.target.value },
                        })
                      }
                      className="bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-gray-400">Max Trading Days</Label>
                    <Input
                      type="number"
                      value={formData.tradingRules.maxTradingDays}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          tradingRules: { ...formData.tradingRules, maxTradingDays: e.target.value },
                        })
                      }
                      className="bg-[#001a2c] border-[#003a4c] text-white"
                      required
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="mt-6 flex justify-end">
            <Button
              type="submit"
              className="bg-sky-500 hover:bg-sky-600 text-white"
              disabled={isSubmitting}
            >
              <Save className="mr-2 h-4 w-4" />
              {isSubmitting ? "Creating..." : "Create Order"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
} 