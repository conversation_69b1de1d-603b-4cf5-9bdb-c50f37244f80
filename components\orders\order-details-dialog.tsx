"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Save,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  TrendingUp,
  AlertTriangle,
  DollarSign,
} from "lucide-react"

interface OrderDetailsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  order: any // Replace with proper type
  onSave: (updatedOrder: any) => void // Replace with proper type
}

export default function OrderDetailsDialog({ open, onOpenChange, order, onSave }: OrderDetailsDialogProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editedOrder, setEditedOrder] = useState(order)

  const handleSave = () => {
    onSave(editedOrder)
    setIsEditing(false)
  }

  const handleStatusChange = (newStatus: string) => {
    setEditedOrder({ ...editedOrder, status: newStatus })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "running":
        return (
          <Badge className="bg-blue-500/10 text-blue-500 border-blue-500/20">
            <Clock className="mr-1 h-3 w-3" />
            Running
          </Badge>
        )
      case "completed":
        return (
          <Badge className="bg-sky-500/10 text-sky-500 border-sky-500/20">
            <CheckCircle className="mr-1 h-3 w-3" />
            Completed
          </Badge>
        )
      case "failed":
        return (
          <Badge className="bg-red-500/10 text-red-500 border-red-500/20">
            <XCircle className="mr-1 h-3 w-3" />
            Failed
          </Badge>
        )
      case "stage2":
        return (
          <Badge className="bg-purple-500/10 text-purple-500 border-purple-500/20">
            <BarChart3 className="mr-1 h-3 w-3" />
            Stage 2
          </Badge>
        )
      default:
        return <Badge>Unknown</Badge>
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl bg-[#002a3c] border-[#003a4c] text-white">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold">Order {order.id}</h2>
            <p className="text-gray-400">Manage order details and settings</p>
          </div>
          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  className="border-[#003a4c] text-white hover:bg-[#003a4c]"
                  onClick={() => setIsEditing(false)}
                >
                  Cancel
                </Button>
                <Button className="bg-sky-500 hover:bg-sky-600 text-white" onClick={handleSave}>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </Button>
              </>
            ) : (
              <Button
                className="bg-sky-500 hover:bg-sky-600 text-white"
                onClick={() => setIsEditing(true)}
              >
                Edit Order
              </Button>
            )}
          </div>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="bg-[#001a2c] border-b border-[#003a4c]">
            <TabsTrigger value="overview" className="data-[state=active]:text-sky-400">Overview</TabsTrigger>
            <TabsTrigger value="trading-rules" className="data-[state=active]:text-sky-400">Trading Rules</TabsTrigger>
            <TabsTrigger value="metrics" className="data-[state=active]:text-sky-400">Metrics</TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:text-sky-400">History</TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Order Details */}
              <Card className="bg-[#001a2c] border-[#003a4c]">
                <CardHeader>
                  <CardTitle className="text-white">Order Details</CardTitle>
                  <CardDescription className="text-gray-400">Basic order information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-gray-400">Status</Label>
                    {isEditing ? (
                      <Select value={editedOrder.status} onValueChange={handleStatusChange}>
                        <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                          <SelectItem value="running">Running</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="failed">Failed</SelectItem>
                          <SelectItem value="stage2">Stage 2</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      getStatusBadge(order.status)
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-400">Type</Label>
                    {isEditing ? (
                      <Select value={editedOrder.type} onValueChange={(v) => setEditedOrder({ ...editedOrder, type: v })}>
                        <SelectTrigger className="bg-[#001a2c] border-[#003a4c] text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="bg-[#002a3c] border-[#003a4c] text-white">
                          <SelectItem value="Standard Challenge">Standard Challenge</SelectItem>
                          <SelectItem value="Express Challenge">Express Challenge</SelectItem>
                          <SelectItem value="HFT Challenge">HFT Challenge</SelectItem>
                        </SelectContent>
                      </Select>
                    ) : (
                      <div className="text-white">{order.type}</div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label className="text-gray-400">Amount</Label>
                    {isEditing ? (
                      <div className="relative">
                        <DollarSign className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
                        <Input
                          value={editedOrder.amount}
                          onChange={(e) => setEditedOrder({ ...editedOrder, amount: e.target.value })}
                          className="pl-10 bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      </div>
                    ) : (
                      <div className="text-white">{order.amount}</div>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-gray-400">Start Date</Label>
                      {isEditing ? (
                        <Input
                          type="date"
                          value={editedOrder.startDate}
                          onChange={(e) => setEditedOrder({ ...editedOrder, startDate: e.target.value })}
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      ) : (
                        <div className="text-white">{order.startDate}</div>
                      )}
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">End Date</Label>
                      {isEditing ? (
                        <Input
                          type="date"
                          value={editedOrder.endDate}
                          onChange={(e) => setEditedOrder({ ...editedOrder, endDate: e.target.value })}
                          className="bg-[#001a2c] border-[#003a4c] text-white"
                        />
                      ) : (
                        <div className="text-white">{order.endDate}</div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* User Information */}
              <Card className="bg-[#001a2c] border-[#003a4c]">
                <CardHeader>
                  <CardTitle className="text-white">User Information</CardTitle>
                  <CardDescription className="text-gray-400">Associated trader details</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16">
                      <AvatarImage src={order.userAvatar} />
                      <AvatarFallback className="bg-[#003a4c] text-white">
                        {order.userName
                          .split(" ")
                          .map((n: string) => n[0])
                          .join("")}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="text-lg font-medium text-white">{order.userName}</div>
                      <div className="text-sm text-gray-400">{order.userId}</div>
                    </div>
                  </div>

                  <div className="pt-4 space-y-4">
                    <div className="flex justify-between items-center">
                      <div className="text-gray-400">Total Profit/Loss</div>
                      <div className={order.profit.startsWith("+") ? "text-sky-400" : "text-red-400"}>
                        {order.profit}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-gray-400">Current Drawdown</div>
                      <div className="text-white">{order.drawdown}</div>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="text-gray-400">Days Remaining</div>
                      <div className="text-white">{order.daysRemaining}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="trading-rules">
            <Card className="bg-[#001a2c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Trading Rules</CardTitle>
                <CardDescription className="text-gray-400">Challenge rules and requirements</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    {/* Trading rules content */}
                    <div className="space-y-2">
                      <Label className="text-gray-400">Maximum Daily Loss</Label>
                      <div className="text-white">${order.tradingRules?.maxDailyLoss || "N/A"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Maximum Total Loss</Label>
                      <div className="text-white">${order.tradingRules?.maxTotalLoss || "N/A"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Profit Target</Label>
                      <div className="text-white">${order.tradingRules?.profitTarget || "N/A"}</div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-gray-400">Minimum Trading Days</Label>
                      <div className="text-white">{order.tradingRules?.minTradingDays || "N/A"} days</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Maximum Trading Days</Label>
                      <div className="text-white">{order.tradingRules?.maxTradingDays || "N/A"} days</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="metrics">
            <Card className="bg-[#001a2c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Trading Metrics</CardTitle>
                <CardDescription className="text-gray-400">Performance statistics and analytics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-gray-400">Total Trades</Label>
                      <div className="text-2xl font-bold text-white">{order.metrics?.totalTrades || "N/A"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Win Rate</Label>
                      <div className="text-2xl font-bold text-sky-400">{order.metrics?.winRate || "N/A"}%</div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-gray-400">Average Win</Label>
                      <div className="text-2xl font-bold text-green-400">${order.metrics?.averageWin || "N/A"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Average Loss</Label>
                      <div className="text-2xl font-bold text-red-400">${order.metrics?.averageLoss || "N/A"}</div>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-gray-400">Largest Win</Label>
                      <div className="text-2xl font-bold text-green-400">${order.metrics?.largestWin || "N/A"}</div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-gray-400">Largest Loss</Label>
                      <div className="text-2xl font-bold text-red-400">${order.metrics?.largestLoss || "N/A"}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history">
            <Card className="bg-[#001a2c] border-[#003a4c]">
              <CardHeader>
                <CardTitle className="text-white">Order History</CardTitle>
                <CardDescription className="text-gray-400">Timeline of order events and changes</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      date: "2024-05-15 09:00",
                      event: "Order Created",
                      description: "Trading challenge initiated",
                      icon: CheckCircle,
                      iconColor: "text-sky-400",
                    },
                    {
                      date: "2024-05-15 09:15",
                      event: "First Trade",
                      description: "First trade executed: +$250",
                      icon: TrendingUp,
                      iconColor: "text-green-400",
                    },
                    {
                      date: "2024-05-16 14:30",
                      event: "Drawdown Alert",
                      description: "Daily drawdown reached 80% of limit",
                      icon: AlertTriangle,
                      iconColor: "text-yellow-400",
                    },
                  ].map((item, index) => (
                    <div key={index} className="flex items-start space-x-4">
                      <div className={`mt-1 ${item.iconColor}`}>
                        <item.icon className="h-5 w-5" />
                      </div>
                      <div>
                        <div className="text-sm text-gray-400">{item.date}</div>
                        <div className="text-white font-medium">{item.event}</div>
                        <div className="text-sm text-gray-400">{item.description}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
} 