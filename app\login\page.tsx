"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { EyeIcon, EyeOffIcon, LockIcon, MailIcon, TrendingUp, Users, DollarSign, BarChart3, Globe, Zap, Shield, ArrowRight } from "lucide-react"
import { motion } from "framer-motion"

export default function LoginPage() {
  const router = useRouter()
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [rememberMe, setRememberMe] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [windowSize, setWindowSize] = useState({ width: 1200, height: 800 })

  const [marketData, setMarketData] = useState({
    activeTraders: "12,847",
    totalVolume: "$2.4B",
    successRate: "94.2%",
    avgProfit: "$1,247"
  })

  // Handle window size for client-side rendering
  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWindowSize({ width: window.innerWidth, height: window.innerHeight })

      const handleResize = () => {
        setWindowSize({ width: window.innerWidth, height: window.innerHeight })
      }

      window.addEventListener('resize', handleResize)
      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    // Simulate API call
    setTimeout(() => {
      setIsLoading(false)
      router.push("/dashboard")
    }, 1500)
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#001a2c] to-[#000c14] p-4 relative overflow-hidden">
      {/* Background Trading Elements */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-10 w-32 h-32 bg-sky-500/20 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/20 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-purple-500/20 rounded-full blur-2xl"></div>
      </div>

      {/* Animated Background Bubbles */}
      <div className="absolute inset-0 overflow-hidden">
        {Array.from({ length: 20 }).map((_, i) => (
          <motion.div
            key={i}
            className="absolute rounded-full bg-sky-500/10"
            initial={{
              width: Math.random() * 100 + 20,
              height: Math.random() * 100 + 20,
              x: Math.random() * windowSize.width,
              y: windowSize.height + 100,
              opacity: Math.random() * 0.5 + 0.1,
            }}
            animate={{
              y: -200,
              opacity: 0,
            }}
            transition={{
              duration: Math.random() * 10 + 15,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 5,
            }}
          />
        ))}
      </div>

      <div className="w-full max-w-6xl grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
        {/* Left Side - Trading Stats & Info */}
        <motion.div
          className="hidden lg:block"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <div className="space-y-8">
            <motion.div variants={itemVariants}>
              <h1 className="text-5xl font-bold text-white mb-4">
                Welcome to <span className="text-sky-400">FUNDEDWHALES</span>
              </h1>
              <p className="text-xl text-gray-300 mb-8">
                Access your account and start trading with the world's most advanced platform. 
                Join thousands of successful traders worldwide.
              </p>
            </motion.div>

            {/* Trading Statistics */}
            <motion.div variants={itemVariants} className="grid grid-cols-2 gap-6">
              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-sky-500/20 rounded-lg flex items-center justify-center">
                    <Users className="h-5 w-5 text-sky-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.activeTraders}</p>
                    <p className="text-sm text-gray-400">Active Traders</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <DollarSign className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.totalVolume}</p>
                    <p className="text-sm text-gray-400">Total Volume</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-purple-500/20 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-5 w-5 text-purple-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.successRate}</p>
                    <p className="text-sm text-gray-400">Success Rate</p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-900/50 rounded-xl p-6 border border-sky-800/30">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-10 h-10 bg-orange-500/20 rounded-lg flex items-center justify-center">
                    <BarChart3 className="h-5 w-5 text-orange-400" />
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-white">{marketData.avgProfit}</p>
                    <p className="text-sm text-gray-400">Avg Profit</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Platform Features */}
            <motion.div variants={itemVariants} className="space-y-4">
              <h3 className="text-xl font-semibold text-white mb-4">Platform Features</h3>
              <div className="grid grid-cols-1 gap-3">
                <div className="flex items-center space-x-3 text-gray-300">
                  <Zap className="h-5 w-5 text-sky-400" />
                  <span>Real-time market data & advanced charting</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <Globe className="h-5 w-5 text-sky-400" />
                  <span>Global market access 24/7</span>
                </div>
                <div className="flex items-center space-x-3 text-gray-300">
                  <Shield className="h-5 w-5 text-sky-400" />
                  <span>Bank-grade security & encryption</span>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Login Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="w-full max-w-md mx-auto z-10"
        >
          <Card className="bg-gradient-to-br from-[#002a3c] to-[#001a2c] border-[#004c66] shadow-2xl">
            <CardHeader className="space-y-1 text-center">
              <div className="flex items-center justify-center space-x-3 mb-4">
                <Image
                  src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                  alt="Funded Whales Logo"
                  width={48}
                  height={48}
                  className="w-12 h-12"
                />
                <div>
                  <CardTitle className="text-2xl font-bold text-white">
                    Welcome Back
                  </CardTitle>
                  <CardDescription className="text-gray-400">Access your trading account and continue your journey</CardDescription>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="email" className="w-full">
                <TabsList className="grid grid-cols-2 mb-6 bg-[#001a2c]">
                  <TabsTrigger value="email" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
                    Email
                  </TabsTrigger>
                  <TabsTrigger value="phone" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
                    Phone
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="email">
                  <form onSubmit={handleLogin} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white">
                        Email
                      </Label>
                      <div className="relative">
                        <MailIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          required
                          className="pl-10 bg-[#001a2c] border-[#003a4c] text-white h-12"
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <Label htmlFor="password" className="text-white">
                          Password
                        </Label>
                        <Link href="/auth/forgot-password" className="text-sm text-sky-400 hover:underline">
                          Forgot password?
                        </Link>
                      </div>
                      <div className="relative">
                        <LockIcon className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          id="password"
                          type={showPassword ? "text" : "password"}
                          placeholder="••••••••"
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          required
                          className="pl-10 pr-10 bg-[#001a2c] border-[#003a4c] text-white h-12"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-3 text-gray-400 hover:text-white"
                        >
                          {showPassword ? <EyeOffIcon className="h-5 w-5" /> : <EyeIcon className="h-5 w-5" />}
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="remember"
                        checked={rememberMe}
                        onCheckedChange={(checked) => setRememberMe(checked as boolean)}
                        className="data-[state=checked]:bg-sky-500 data-[state=checked]:border-sky-500"
                      />
                      <label
                        htmlFor="remember"
                        className="text-sm font-medium leading-none text-gray-400 peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        Remember me
                      </label>
                    </div>

                    <Button
                      type="submit"
                      className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium"
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <motion.div
                          className="h-5 w-5 border-2 border-white border-t-transparent rounded-full"
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                        />
                      ) : (
                        <>
                          Sign In
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </>
                      )}
                    </Button>
                  </form>
                </TabsContent>

                <TabsContent value="phone">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone" className="text-white">
                        Phone Number
                      </Label>
                      <Input
                        id="phone"
                        type="tel"
                        placeholder="+****************"
                        className="bg-[#001a2c] border-[#003a4c] text-white h-12"
                      />
                    </div>
                    <Button className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white h-12 rounded-xl font-medium">
                      Send Code
                    </Button>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
            <CardFooter>
              <p className="text-center text-gray-400 text-sm w-full">
                Don't have an account?{" "}
                <Link href="/signup" className="text-sky-400 hover:text-sky-300 font-medium transition-colors">
                  Create Account
                </Link>
              </p>
            </CardFooter>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
