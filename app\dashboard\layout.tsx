"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Sidebar from "@/components/dashboard/sidebar"
import Header from "@/components/dashboard/header"
import ProtectedRoute from "@/components/protected-route"

export default function DashboardLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const [sidebarWidth, setSidebarWidth] = useState(220)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
      if (window.innerWidth < 1024) {
        setSidebarWidth(0)
      } else {
        setSidebarWidth(isCollapsed ? 56 : 220)
      }
    }

    checkMobile()
    window.addEventListener("resize", checkMobile)
    return () => window.removeEventListener("resize", checkMobile)
  }, [isCollapsed])

  const handleSidebarToggle = (collapsed: boolean) => {
    setIsCollapsed(collapsed)
    if (!isMobile) {
      setSidebarWidth(collapsed ? 56 : 220)
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] relative">
        <div
          className="flex flex-col transition-all duration-500 ease-in-out min-h-screen"
          style={{
            marginRight: isMobile ? 0 : `${sidebarWidth}px`,
            paddingRight: isMobile ? 0 : '1rem'
          }}
        >
          <main className="flex-1 w-full overflow-x-hidden">{children}</main>
        </div>
        <Sidebar onToggle={handleSidebarToggle} />
      </div>
    </ProtectedRoute>
  )
}
