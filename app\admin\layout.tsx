import type React from "react"
import AdminSidebar from "@/components/admin/sidebar"
import ProtectedRoute from "@/components/protected-route"

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c]">
      <AdminSidebar />
      <div className="lg:ml-[280px]">
        <main className="p-6">{children}</main>
      </div>
    </div>
    </ProtectedRoute>
  )
}
