"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Menu, X, Globe } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useAuth } from "@/lib/auth-context"

export default function Navbar() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  return (
    <header
      className={`sticky top-0 z-40 w-full transition-all duration-300 ${
        isScrolled ? "bg-transparent backdrop-blur-sm" : "bg-transparent"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="https://res.cloudinary.com/dufcjjaav/image/upload/v1751695664/logo-removebg-preview_eqaddm.png"
                alt="Funded Whales Logo"
                width={80}
                height={80}
                className="w-16 h-16"
              />
              <span className="text-xl font-bold text-white hidden sm:block">
                FUNDED<span className="text-sky-400">WHALES</span>
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/challenge" className="text-sm text-gray-300 hover:text-white transition-colors">
              Challenge
            </Link>
            <Link href="/rules" className="text-sm text-gray-300 hover:text-white transition-colors">
              Rules
            </Link>
            <Link href="/support" className="text-sm text-gray-300 hover:text-white transition-colors">
              Support
            </Link>
            <Link href="/old-dashboard" className="text-sm text-gray-300 hover:text-white transition-colors">
              Old Dashboard
            </Link>
          </nav>

          <div className="hidden md:flex items-center space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
                  <Globe className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>English</DropdownMenuItem>
                <DropdownMenuItem>Spanish</DropdownMenuItem>
                <DropdownMenuItem>French</DropdownMenuItem>
                <DropdownMenuItem>German</DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
            {isAuthenticated ? (
              <Link href="/dashboard">
                <Button className="bg-sky-500 hover:bg-sky-600 text-white">Dashboard</Button>
              </Link>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button variant="outline" className="border-sky-500/30 text-white hover:bg-sky-500/10">Login</Button>
                </Link>
                <Link href="/auth/signup">
                  <Button className="bg-sky-500 hover:bg-sky-600 text-white">Sign Up</Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}>
              {isMobileMenuOpen ? <X className="h-6 w-6 text-white" /> : <Menu className="h-6 w-6 text-white" />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden bg-transparent backdrop-blur-sm border-t border-gray-800/30">
          <div className="container mx-auto px-4 py-4 space-y-4">
            <Link
              href="/challenge"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Challenge
            </Link>
            <Link
              href="/rules"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Rules
            </Link>
            <Link
              href="/support"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Support
            </Link>
            <Link
              href="/old-dashboard"
              className="block text-gray-300 hover:text-white transition-colors"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Old Dashboard
            </Link>
            <div className="pt-4 border-t border-gray-800 flex items-center justify-between">
              <Button variant="ghost" size="icon" className="text-gray-300 hover:text-white">
                <Globe className="h-5 w-5" />
              </Button>
              {isAuthenticated ? (
                <Link href="/dashboard">
                  <Button className="bg-sky-500 hover:bg-sky-600 text-white">Dashboard</Button>
                </Link>
              ) : (
                <>
                  <Link href="/login">
                    <Button variant="outline" className="border-sky-500/30 text-white hover:bg-sky-500/10">Login</Button>
                  </Link>
                  <Link href="/signup">
                    <Button className="bg-sky-500 hover:bg-sky-600 text-white">Sign Up</Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      )}
    </header>
  )
}
