"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { Star, Crown, Diamond, Quote, Award, TrendingUp, CheckCircle, DollarSign, Calendar, MapPin, Clock, TrendingDown } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import Autoplay from "embla-carousel-autoplay"

export default function TestimonialsSection() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Forex Trader",
      content:
        "The platform is exactly what I was looking for. Clear rules, fair evaluation process, and reliable payouts. Started with the $25K challenge and passed within 3 weeks. The support team is responsive and helpful.",
      rating: 5,
      location: "New York, USA",
      profit: "$8,500",
      accountSize: "$25K",
      timeToPass: "3 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "March 2024",
      strategy: "EUR/USD & GBP/USD",
    },
    {
      name: "<PERSON>",
      role: "Swing Trader",
      content:
        "Professional platform with transparent rules. The evaluation process was straightforward and the profit targets are realistic. I appreciate the educational resources and community support.",
      rating: 5,
      location: "London, UK",
      profit: "$12,300",
      accountSize: "$50K",
      timeToPass: "4 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "February 2024",
      strategy: "Gold & Oil",
    },
    {
      name: "David K.",
      role: "Day Trader",
      content:
        "Good execution speed and reliable platform. The rules are clear and the evaluation process is fair. I've been trading here for 6 months and the payouts are always on time.",
      rating: 5,
      location: "Toronto, Canada",
      profit: "$15,800",
      accountSize: "$100K",
      timeToPass: "5 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "January 2024",
      strategy: "USD/JPY & EUR/JPY",
    },
    {
      name: "Emma W.",
      role: "Futures Trader",
      content:
        "Solid platform with good customer service. The evaluation was challenging but fair. I like the transparency and the fact that there are no hidden fees. Recommended for serious traders.",
      rating: 5,
      location: "Chicago, USA",
      profit: "$9,200",
      accountSize: "$25K",
      timeToPass: "3 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "December 2023",
      strategy: "ES & NQ futures",
    },
    {
      name: "Alex T.",
      role: "Algorithmic Trader",
      content:
        "The platform supports my trading style well. Good API integration and stable execution. The profit sharing model is transparent and the support team understands trading.",
      rating: 5,
      location: "Singapore",
      profit: "$18,400",
      accountSize: "$200K",
      timeToPass: "6 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "November 2023",
      strategy: "Automated trading",
    },
    {
      name: "Lisa M.",
      role: "Options Trader",
      content:
        "Reliable platform with good execution. The evaluation process was clear and the rules are straightforward. I've had a positive experience with the support team.",
      rating: 5,
      location: "Sydney, Australia",
      profit: "$11,600",
      accountSize: "$50K",
      timeToPass: "4 weeks",
      badge: "Verified",
      verified: true,
      joinDate: "October 2023",
      strategy: "SPY options",
    },
  ]

  return (
    <section className="py-28 bg-gradient-to-b from-[#001a2c] via-[#001e30] to-[#002235] relative overflow-hidden">
      {/* Luxury background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-16 w-80 h-80 bg-sky-500/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-16 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[700px] h-[700px] bg-gradient-radial from-sky-500/3 to-transparent rounded-full"></div>
      </div>

      {/* Floating luxury elements */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(25)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1.5 h-1.5 bg-sky-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-25, -100],
              opacity: [0, 0.7, 0],
              scale: [0.4, 1.1, 0.4],
            }}
            transition={{
              duration: 7 + Math.random() * 5,
              repeat: Number.POSITIVE_INFINITY,
              delay: Math.random() * 7,
              ease: "easeInOut",
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <div className="flex items-center justify-center mb-4">
            <span className="text-sky-400 font-medium tracking-wider uppercase text-sm flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Verified Trader Stories
            </span>
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-white">
            What Our{" "}
            <span className="bg-gradient-to-r from-sky-400 to-blue-400 bg-clip-text text-transparent">
              Traders Say
            </span>
          </h2>
          <p className="text-lg text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Real feedback from verified traders who have successfully completed our evaluation process and are actively trading with funded accounts.
          </p>
        </motion.div>

        <Carousel
          opts={{
            align: "start",
            loop: true,
          }}
          plugins={[
            Autoplay({
              delay: 8000,
            }),
          ]}
          className="w-full max-w-7xl mx-auto"
        >
          <CarouselContent className="-ml-4">
            {testimonials.map((testimonial, index) => (
              <CarouselItem key={index} className="pl-4 md:basis-1/2 lg:basis-1/3">
                <motion.div
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="group h-full"
                >
                  <Card className="bg-gradient-to-br from-[#002a3c]/90 to-[#001e30]/90 border border-sky-500/20 h-full hover:border-sky-400/40 transition-all duration-300 relative overflow-hidden">
                    {/* Verified badge */}
                    {testimonial.verified && (
                      <div className="absolute top-4 right-4 z-10">
                        <div className="bg-sky-500/20 border border-sky-400/40 rounded-full p-1">
                          <CheckCircle className="h-4 w-4 text-sky-400" />
                        </div>
                      </div>
                    )}
                    
                    <CardContent className="p-6">
                      {/* Rating stars */}
                      <div className="flex items-center mb-4">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-sky-400 text-sky-400 mr-1" />
                        ))}
                        <span className="text-xs text-gray-400 ml-2">Verified Trader</span>
                      </div>

                      {/* Testimonial content */}
                      <div className="relative mb-4">
                        <Quote className="absolute -top-2 -left-2 h-6 w-6 text-sky-400/30" />
                        <p className="text-gray-300 leading-relaxed pl-4 text-sm">
                        "{testimonial.content}"
                      </p>
                      </div>

                      {/* Trader stats */}
                      <div className="grid grid-cols-2 gap-3 mb-4 text-xs">
                        <div className="flex items-center gap-2 text-gray-400">
                          <DollarSign className="h-3 w-3" />
                          <span>Account: {testimonial.accountSize}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <Calendar className="h-3 w-3" />
                          <span>Joined: {testimonial.joinDate}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <Clock className="h-3 w-3" />
                          <span>Passed: {testimonial.timeToPass}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-400">
                          <TrendingUp className="h-3 w-3" />
                          <span>Strategy: {testimonial.strategy}</span>
                        </div>
                      </div>

                      {/* Trader info */}
                      <div className="border-t border-sky-500/20 pt-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-semibold text-white flex items-center gap-2">
                              {testimonial.name}
                              {testimonial.badge === "Verified" && <CheckCircle className="h-4 w-4 text-sky-400" />}
                            </p>
                            <p className="text-sm text-sky-400">{testimonial.role}</p>
                            <div className="flex items-center gap-1 text-xs text-gray-400">
                              <MapPin className="h-3 w-3" />
                              {testimonial.location}
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-sky-400">{testimonial.profit}</p>
                            <p className="text-xs text-gray-400">Total Profits</p>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
          <CarouselNext className="bg-gradient-to-r from-sky-500/20 to-blue-500/20 border-sky-400/30 text-sky-400 hover:bg-sky-500/30" />
        </Carousel>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 max-w-5xl mx-auto">
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">$850K+</div>
              <div className="text-gray-300 text-sm">Total Profits Paid</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">5,000+</div>
              <div className="text-gray-300 text-sm">Active Traders</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">92%</div>
              <div className="text-gray-300 text-sm">Success Rate</div>
            </div>
            <div className="bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-400/20 rounded-xl px-6 py-6">
              <div className="text-3xl font-bold text-sky-400 mb-2">48h</div>
              <div className="text-gray-300 text-sm">Avg. Payout Time</div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
