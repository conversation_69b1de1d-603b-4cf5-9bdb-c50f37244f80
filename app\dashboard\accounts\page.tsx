"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  BarChart3,
  ChevronRight,
  Clock,
  Download,
  ExternalLink,
  Eye,
  FileText,
  Lock,
  Plus,
  Settings,
  ShieldCheck,
  TrendingUp,
} from "lucide-react"
import { Progress } from "@/components/ui/progress"

export default function AccountsPage() {
  const [selectedAccount, setSelectedAccount] = useState<string | null>(null)

  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  const accounts = [
    {
      id: "acc1",
      name: "Standard $100K",
      balance: "$98,450",
      profit: "+$4,200",
      profitPercentage: "4.2%",
      drawdown: "1.55%",
      status: "active",
      phase: "Challenge Phase 1",
      platform: "MetaTrader 5",
      login: "FW78542196",
      password: "••••••••",
      server: "FundedWhales-Live1",
      progress: 52.5,
    },
    {
      id: "acc2",
      name: "HFT Neo $50K",
      balance: "$51,250",
      profit: "+$2,800",
      profitPercentage: "5.6%",
      drawdown: "0.8%",
      status: "active",
      phase: "Challenge Phase 2",
      platform: "MetaTrader 5",
      login: "FW78542197",
      password: "••••••••",
      server: "FundedWhales-Live2",
      progress: 70,
    },
    {
      id: "acc3",
      name: "Standard $25K",
      balance: "$0",
      profit: "$0",
      profitPercentage: "0%",
      drawdown: "0%",
      status: "expired",
      phase: "Challenge Phase 1",
      platform: "MetaTrader 5",
      login: "FW78542198",
      password: "••••••••",
      server: "FundedWhales-Live1",
      progress: 0,
    },
  ]

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-white">Trading Accounts</h2>
        <Button className="bg-gradient-to-r from-sky-500 to-sky-400 hover:from-sky-600 hover:to-sky-500 text-white">
          <Plus className="mr-2 h-4 w-4" />
          New Challenge
        </Button>
      </div>

      <Tabs defaultValue="active" className="w-full">
        <TabsList className="bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 backdrop-blur-lg border border-sky-900/60">
          <TabsTrigger value="active" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Active Accounts
          </TabsTrigger>
          <TabsTrigger value="funded" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Funded Accounts
          </TabsTrigger>
          <TabsTrigger value="expired" className="data-[state=active]:bg-sky-500 data-[state=active]:text-white">
            Expired Accounts
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="grid grid-cols-1 gap-6">
            {accounts
              .filter((account) => account.status === "active")
              .map((account, index) => (
                <motion.div
                  key={account.id}
                  variants={fadeInUp}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.1 }}
                >
                  <Card
                    className={`bg-gradient-to-br from-[#001a2c]/80 via-[#002a3c]/80 to-[#003a4c]/80 backdrop-blur-lg border border-sky-900/60 cursor-pointer transition-all duration-200 hover:border-sky-500/50 ${
                      selectedAccount === account.id ? "border-sky-500" : ""
                    }`}
                    onClick={() => setSelectedAccount(selectedAccount === account.id ? null : account.id)}
                  >
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white">{account.name}</CardTitle>
                          <CardDescription className="text-gray-300">{account.phase}</CardDescription>
                        </div>
                        <Badge
                          className={`${
                            account.status === "active"
                              ? "bg-green-500/20 text-green-400"
                              : account.status === "funded"
                                ? "bg-sky-500/20 text-sky-400"
                                : "bg-gray-500/20 text-gray-400"
                          }`}
                        >
                          {account.status === "active" ? "Active" : account.status === "funded" ? "Funded" : "Expired"}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-400">Balance</p>
                          <p className="text-xl font-bold text-white">{account.balance}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit</p>
                          <p className="text-xl font-bold text-green-500">{account.profit}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit Target (8%)</p>
                          <div className="flex items-center">
                            <p className="text-xl font-bold text-white mr-2">{account.profitPercentage}</p>
                            <Progress value={account.progress} className="h-2 w-20 bg-gray-700">
                              <div className="h-full bg-sky-500" style={{ width: `${account.progress}%` }} />
                            </Progress>
                          </div>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Max Drawdown</p>
                          <p className="text-xl font-bold text-white">{account.drawdown}</p>
                        </div>
                      </div>

                      {selectedAccount === account.id && (
                        <motion.div
                          initial={{ opacity: 0, height: 0 }}
                          animate={{ opacity: 1, height: "auto" }}
                          exit={{ opacity: 0, height: 0 }}
                          transition={{ duration: 0.3 }}
                          className="mt-4 pt-4 border-t border-sky-900/60"
                        >
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-4">
                              <div>
                                <h3 className="text-white font-medium mb-2">Account Details</h3>
                                <div className="space-y-2">
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Platform</span>
                                    <span className="text-white">{account.platform}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Login ID</span>
                                    <span className="text-white">{account.login}</span>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Password</span>
                                    <div className="flex items-center">
                                      <span className="text-white mr-2">{account.password}</span>
                                      <Button variant="ghost" size="icon" className="h-5 w-5 text-gray-400">
                                        <Eye className="h-4 w-4" />
                                      </Button>
                                    </div>
                                  </div>
                                  <div className="flex justify-between">
                                    <span className="text-gray-400">Server</span>
                                    <span className="text-white">{account.server}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                            <div className="space-y-4">
                              <div>
                                <h3 className="text-white font-medium mb-2">Quick Actions</h3>
                                <div className="space-y-2">
                                  <Button className="w-full bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 border border-blue-500/30">
                                    <ExternalLink className="h-4 w-4 mr-2" />
                                    Open Platform
                                  </Button>
                                  <Button className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/20">
                                    <Download className="h-4 w-4 mr-2" />
                                    Download Statement
                                  </Button>
                                  <Button className="w-full bg-white/10 hover:bg-white/20 text-white border border-white/20">
                                    <Settings className="h-4 w-4 mr-2" />
                                    Account Settings
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
          </div>
        </TabsContent>

        <TabsContent value="funded">
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <TrendingUp className="h-8 w-8 text-blue-400" />
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">No Funded Accounts Yet</h3>
            <p className="text-gray-400 mb-6">Complete your challenge phases to get funded accounts</p>
            <Button className="bg-blue-500 hover:bg-blue-600 text-white">
              <Plus className="mr-2 h-4 w-4" />
              Start New Challenge
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="expired">
          <div className="grid grid-cols-1 gap-6">
            {accounts
              .filter((account) => account.status === "expired")
              .map((account, index) => (
                <motion.div
                  key={account.id}
                  variants={fadeInUp}
                  initial="hidden"
                  animate="visible"
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="bg-white/10 backdrop-blur-lg border border-white/20">
                    <CardHeader className="pb-2">
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-white">{account.name}</CardTitle>
                          <CardDescription className="text-gray-300">{account.phase}</CardDescription>
                        </div>
                        <Badge className="bg-gray-500/20 text-gray-400">Expired</Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <p className="text-sm text-gray-400">Balance</p>
                          <p className="text-xl font-bold text-white">{account.balance}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Profit</p>
                          <p className="text-xl font-bold text-gray-400">{account.profit}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Progress</p>
                          <p className="text-xl font-bold text-gray-400">{account.profitPercentage}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-400">Status</p>
                          <p className="text-xl font-bold text-gray-400">Expired</p>
                        </div>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full bg-blue-500 hover:bg-blue-600 text-white">
                        <Plus className="mr-2 h-4 w-4" />
                        Restart Challenge
                      </Button>
                    </CardFooter>
                  </Card>
                </motion.div>
              ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
